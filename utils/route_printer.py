"""
Route printing utilities for FastAPI applications.

This module provides functionality to display registered routes in a formatted way.
"""

import logging
from typing import List, Dict, Any
from fastapi import FastAPI

logger = logging.getLogger(__name__)


def print_routes(app: FastAPI) -> None:
    """
    Print all registered routes with their methods, paths, and handlers.
    
    Args:
        app: FastAPI application instance
    """
    print("=" * 80)
    print("📋 REGISTERED ROUTES")
    print("=" * 80)
    
    routes_info = _extract_routes_info(app)
    _print_routes_by_tag(routes_info)
    
    print("=" * 80)
    print(f"✅ Total routes registered: {len(routes_info)}")
    print("=" * 80)
    
    # 同时记录到日志
    _log_routes_info(routes_info)


def _extract_routes_info(app: FastAPI) -> List[Dict[str, Any]]:
    """
    Extract route information from FastAPI app.
    
    Args:
        app: FastAPI application instance
        
    Returns:
        List of route information dictionaries
    """
    routes_info = []
    
    for route in app.routes:
        if hasattr(route, 'methods') and hasattr(route, 'path'):
            # Skip the auto-generated HEAD and OPTIONS methods
            methods = [method for method in route.methods if method not in ['HEAD', 'OPTIONS']]
            if methods:
                route_info = {
                    'methods': methods,
                    'path': route.path,
                    'name': getattr(route, 'name', 'N/A'),
                    'tags': getattr(route, 'tags', [])
                }
                routes_info.append(route_info)
    
    # Sort routes by path for better readability
    routes_info.sort(key=lambda x: x['path'])
    return routes_info


def _print_routes_by_tag(routes_info: List[Dict[str, Any]]) -> None:
    """
    Print routes grouped by their tags.
    
    Args:
        routes_info: List of route information dictionaries
    """
    current_tag = None
    
    for route_info in routes_info:
        tags = route_info['tags']
        tag = tags[0] if tags else 'General'
        
        if tag != current_tag:
            print(f"\n🏷️  {tag.upper()} ENDPOINTS:")
            print("-" * 50)
            current_tag = tag
            
        methods_str = ', '.join(route_info['methods'])
        print(f"  {methods_str:<8} {route_info['path']:<40} ({route_info['name']})")


def _log_routes_info(routes_info: List[Dict[str, Any]]) -> None:
    """
    Log route information to the logger.
    
    Args:
        routes_info: List of route information dictionaries
    """
    logger.info(f"Total routes registered: {len(routes_info)}")
    for route_info in routes_info:
        methods_str = ', '.join(route_info['methods'])
        logger.info(f"Route: {methods_str} {route_info['path']} ({route_info['name']}) - Tags: {route_info['tags']}")