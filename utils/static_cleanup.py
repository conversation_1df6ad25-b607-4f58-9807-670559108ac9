"""
Static file cleanup utilities for the Document Converter Service.

This module provides functionality to clean up old generated files and manage
disk space usage in the static directory.
"""

import logging
import os
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Tuple

logger = logging.getLogger(__name__)


class StaticFileManager:
    """Manager for static file cleanup and maintenance."""
    
    def __init__(self, static_dir: str = "static", max_age_days: int = 7, max_files: int = 1000):
        """
        Initialize the static file manager.
        
        Args:
            static_dir: Path to static directory
            max_age_days: Maximum age of files to keep (days)
            max_files: Maximum number of result directories to keep
        """
        self.static_dir = Path(static_dir)
        self.max_age_days = max_age_days
        self.max_files = max_files
        
    def cleanup_old_files(self) -> Tuple[int, int]:
        """
        Clean up old files based on age and count limits.
        
        Returns:
            Tuple of (deleted_dirs_count, freed_bytes)
        """
        if not self.static_dir.exists():
            logger.info(f"Static directory {self.static_dir} does not exist")
            return 0, 0
            
        deleted_count = 0
        freed_bytes = 0
        
        # Get all result directories
        result_dirs = self._get_result_directories()
        
        if not result_dirs:
            logger.info("No result directories found for cleanup")
            return 0, 0
            
        # Clean by age
        cutoff_time = datetime.now() - timedelta(days=self.max_age_days)
        age_deleted, age_freed = self._cleanup_by_age(result_dirs, cutoff_time)
        deleted_count += age_deleted
        freed_bytes += age_freed
        
        # Clean by count (keep most recent)
        remaining_dirs = self._get_result_directories()  # Refresh after age cleanup
        if len(remaining_dirs) > self.max_files:
            count_deleted, count_freed = self._cleanup_by_count(remaining_dirs)
            deleted_count += count_deleted
            freed_bytes += count_freed
            
        logger.info(f"Cleanup completed: deleted {deleted_count} directories, freed {freed_bytes / 1024 / 1024:.2f} MB")
        return deleted_count, freed_bytes
    
    def _get_result_directories(self) -> List[Tuple[Path, datetime]]:
        """
        Get all result directories with their creation times.
        
        Returns:
            List of (directory_path, creation_time) tuples
        """
        result_dirs = []
        
        for item in self.static_dir.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                # Skip test directories
                if item.name in ['test-img', 'test-office', 'test-pdf']:
                    continue
                    
                try:
                    # Parse timestamp from directory name (format: YYYYMMDDHHMMSS...)
                    timestamp_part = item.name.split('_')[0]
                    if ':' in timestamp_part:
                        # Handle format like "20250611:16:59:03:941"
                        timestamp_part = timestamp_part.replace(':', '')
                    
                    # Extract YYYYMMDDHHMMSS part
                    if len(timestamp_part) >= 14:
                        timestamp_str = timestamp_part[:14]
                        creation_time = datetime.strptime(timestamp_str, '%Y%m%d%H%M%S')
                        result_dirs.append((item, creation_time))
                except (ValueError, IndexError) as e:
                    logger.warning(f"Could not parse timestamp from directory {item.name}: {e}")
                    # Use file system creation time as fallback
                    try:
                        creation_time = datetime.fromtimestamp(item.stat().st_ctime)
                        result_dirs.append((item, creation_time))
                    except OSError:
                        logger.warning(f"Could not get creation time for {item.name}")
                        
        return result_dirs
    
    def _cleanup_by_age(self, result_dirs: List[Tuple[Path, datetime]], cutoff_time: datetime) -> Tuple[int, int]:
        """Clean up directories older than cutoff_time."""
        deleted_count = 0
        freed_bytes = 0
        
        for dir_path, creation_time in result_dirs:
            if creation_time < cutoff_time:
                try:
                    dir_size = self._get_directory_size(dir_path)
                    shutil.rmtree(dir_path)
                    deleted_count += 1
                    freed_bytes += dir_size
                    logger.debug(f"Deleted old directory: {dir_path.name} (age: {datetime.now() - creation_time})")
                except OSError as e:
                    logger.error(f"Failed to delete directory {dir_path}: {e}")
                    
        return deleted_count, freed_bytes
    
    def _cleanup_by_count(self, result_dirs: List[Tuple[Path, datetime]]) -> Tuple[int, int]:
        """Clean up excess directories, keeping the most recent ones."""
        if len(result_dirs) <= self.max_files:
            return 0, 0
            
        # Sort by creation time (newest first)
        result_dirs.sort(key=lambda x: x[1], reverse=True)
        
        # Delete excess directories
        excess_dirs = result_dirs[self.max_files:]
        deleted_count = 0
        freed_bytes = 0
        
        for dir_path, creation_time in excess_dirs:
            try:
                dir_size = self._get_directory_size(dir_path)
                shutil.rmtree(dir_path)
                deleted_count += 1
                freed_bytes += dir_size
                logger.debug(f"Deleted excess directory: {dir_path.name}")
            except OSError as e:
                logger.error(f"Failed to delete directory {dir_path}: {e}")
                
        return deleted_count, freed_bytes
    
    def _get_directory_size(self, dir_path: Path) -> int:
        """Get total size of directory in bytes."""
        total_size = 0
        try:
            for item in dir_path.rglob('*'):
                if item.is_file():
                    total_size += item.stat().st_size
        except OSError:
            pass
        return total_size
    
    def get_usage_stats(self) -> dict:
        """Get current usage statistics of static directory."""
        if not self.static_dir.exists():
            return {
                'total_directories': 0,
                'total_size_bytes': 0,
                'total_size_mb': 0,
                'oldest_file_age_days': 0,
                'newest_file_age_days': 0
            }
            
        result_dirs = self._get_result_directories()
        total_size = sum(self._get_directory_size(path) for path, _ in result_dirs)
        
        stats = {
            'total_directories': len(result_dirs),
            'total_size_bytes': total_size,
            'total_size_mb': total_size / 1024 / 1024,
            'oldest_file_age_days': 0,
            'newest_file_age_days': 0
        }
        
        if result_dirs:
            now = datetime.now()
            creation_times = [creation_time for _, creation_time in result_dirs]
            oldest_time = min(creation_times)
            newest_time = max(creation_times)
            
            stats['oldest_file_age_days'] = (now - oldest_time).days
            stats['newest_file_age_days'] = (now - newest_time).days
            
        return stats


def cleanup_static_files(static_dir: str = "static", max_age_days: int = 7, max_files: int = 1000) -> dict:
    """
    Convenience function to cleanup static files.
    
    Args:
        static_dir: Path to static directory
        max_age_days: Maximum age of files to keep
        max_files: Maximum number of result directories to keep
        
    Returns:
        Dictionary with cleanup results
    """
    manager = StaticFileManager(static_dir, max_age_days, max_files)
    
    # Get stats before cleanup
    before_stats = manager.get_usage_stats()
    
    # Perform cleanup
    deleted_count, freed_bytes = manager.cleanup_old_files()
    
    # Get stats after cleanup
    after_stats = manager.get_usage_stats()
    
    return {
        'deleted_directories': deleted_count,
        'freed_bytes': freed_bytes,
        'freed_mb': freed_bytes / 1024 / 1024,
        'before_stats': before_stats,
        'after_stats': after_stats
    }