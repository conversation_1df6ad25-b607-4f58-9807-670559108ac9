"""
Application lifespan management for the Document Converter Service.

This module handles startup and shutdown events for the FastAPI application.
"""

import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI

import utils.config
from utils.route_printer import print_routes
from utils.static_cleanup import cleanup_static_files
from utils.config import global_config

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager for handling startup and shutdown events.

    Args:
        app: FastAPI application instance
    """
    logger.info("Starting Document Converter Service...")

    try:
        # 启动时的初始化操作
        await _startup_operations(app)
        logger.info("Document Converter Service started successfully")

    except Exception as e:
        logger.error(f"Failed to start service: {str(e)}")
        raise

    yield

    # 关闭时的清理操作
    await _shutdown_operations(app)


async def _startup_operations(app: FastAPI) -> None:
    """
    Perform startup operations.
    
    Args:
        app: FastAPI application instance
    """
    # 打印全局配置
    utils.config.print_global_config()
    
    # 打印注册的路由
    print_routes(app)
    
    # 执行静态文件清理
    try:
        # 从配置读取清理参数，使用默认值
        max_age_days = global_config.getint("cleanup", "max_age_days", fallback=7)
        max_files = global_config.getint("cleanup", "max_files", fallback=1000)
        
        logger.info("Starting static file cleanup...")
        cleanup_result = cleanup_static_files(max_age_days=max_age_days, max_files=max_files)
        
        if cleanup_result['deleted_directories'] > 0:
            logger.info(f"Cleaned up {cleanup_result['deleted_directories']} old directories, "
                       f"freed {cleanup_result['freed_mb']:.2f} MB")
        else:
            logger.info("No old files to clean up")
            
        # 记录当前使用统计
        after_stats = cleanup_result['after_stats']
        logger.info(f"Static directory stats: {after_stats['total_directories']} directories, "
                   f"{after_stats['total_size_mb']:.2f} MB total")
                   
    except Exception as e:
        logger.error(f"Static file cleanup failed: {e}")
        # 不影响服务启动


async def _shutdown_operations(app: FastAPI) -> None:
    """
    Perform shutdown operations and cleanup.
    
    Args:
        app: FastAPI application instance
    """
    logger.info("Shutting down Document Converter Service...")
    
    try:
        # 清理模型资源
        _cleanup_model_resources(app)
        logger.info("Resources cleaned up successfully")
    except Exception as e:
        logger.error(f"Error during cleanup: {str(e)}")


def _cleanup_model_resources(app: FastAPI) -> None:
    """
    Clean up model resources stored in app state.
    
    Args:
        app: FastAPI application instance
    """
    # 清理模型管理器
    if hasattr(app.state, "model_manager"):
        del app.state.model_manager
        logger.debug("Model manager cleaned up")
    
    # 清理dolphin模型
    if hasattr(app.state, "dolphin_model"):
        del app.state.dolphin_model
        logger.debug("Dolphin model cleaned up")
    
    # 可以在此处添加其他模型的清理逻辑
    # 例如：mineru_model, monkey_model等