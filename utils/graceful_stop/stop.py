"""
Graceful shutdown utilities for the document converter service.

This module provides utilities for handling graceful shutdown signals
and ensuring proper cleanup of resources.
"""

import logging
import signal
import sys
import threading
import time

logger = logging.getLogger(__name__)


def handle_signal(signum: int, _) -> None:
    """
    Handle shutdown signals gracefully.

    Args:
        signum: Signal number
        frame: Current stack frame
    """
    signal_name = signal.Signals(signum).name
    logger.info(f"Received {signal_name} signal, shutting down gracefully...")
    print(f"Exiting gracefully after receiving {signal_name}, waiting 15 seconds...")
    time.sleep(15)
    logger.info("Graceful shutdown completed")
    sys.exit(0)


def register_signal() -> None:
    """Register signal handlers for graceful shutdown."""
    signal.signal(signal.SIGTERM, handle_signal)
    signal.signal(signal.SIGINT, handle_signal)
    logger.info("Signal handlers registered for graceful shutdown")


def wait_signal() -> None:
    """
    Wait for shutdown signals.

    This function blocks until a shutdown signal is received.
    """
    register_signal()
    stop_event = threading.Event()
    logger.info("Waiting for shutdown signal...")
    stop_event.wait()
