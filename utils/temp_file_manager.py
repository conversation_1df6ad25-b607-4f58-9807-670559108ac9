"""
Temporary file management utilities for safe file handling.

This module provides context managers and utilities for safe temporary file
operations with automatic cleanup.
"""

import logging
import os
import tempfile
from contextlib import contextmanager
from pathlib import Path
from typing import Generator, Optional

logger = logging.getLogger(__name__)


@contextmanager
def safe_temp_file(suffix: Optional[str] = None, 
                   prefix: Optional[str] = None,
                   dir: Optional[str] = None) -> Generator[str, None, None]:
    """
    Context manager for safe temporary file handling with automatic cleanup.
    
    Args:
        suffix: File suffix (e.g., '.pdf', '.png')
        prefix: File prefix
        dir: Directory to create temp file in
        
    Yields:
        str: Path to the temporary file
        
    Example:
        with safe_temp_file(suffix='.pdf') as tmp_path:
            # Use tmp_path for operations
            process_file(tmp_path)
        # File is automatically cleaned up
    """
    tmp_path = None
    try:
        # Create temporary file
        fd, tmp_path = tempfile.mkstemp(suffix=suffix, prefix=prefix, dir=dir)
        os.close(fd)  # Close file descriptor, keep the path
        
        logger.debug(f"Created temporary file: {tmp_path}")
        yield tmp_path
        
    except Exception as e:
        logger.error(f"Error in temporary file operation: {e}")
        raise
    finally:
        # Cleanup
        if tmp_path and os.path.exists(tmp_path):
            try:
                os.unlink(tmp_path)
                logger.debug(f"Cleaned up temporary file: {tmp_path}")
            except OSError as e:
                logger.warning(f"Failed to cleanup temporary file {tmp_path}: {e}")


@contextmanager 
def safe_temp_dir(suffix: Optional[str] = None,
                  prefix: Optional[str] = None,
                  dir: Optional[str] = None) -> Generator[str, None, None]:
    """
    Context manager for safe temporary directory handling with automatic cleanup.
    
    Args:
        suffix: Directory suffix
        prefix: Directory prefix  
        dir: Parent directory to create temp dir in
        
    Yields:
        str: Path to the temporary directory
        
    Example:
        with safe_temp_dir(prefix='processing_') as tmp_dir:
            # Use tmp_dir for operations
            process_directory(tmp_dir)
        # Directory is automatically cleaned up
    """
    tmp_dir = None
    try:
        # Create temporary directory
        tmp_dir = tempfile.mkdtemp(suffix=suffix, prefix=prefix, dir=dir)
        logger.debug(f"Created temporary directory: {tmp_dir}")
        yield tmp_dir
        
    except Exception as e:
        logger.error(f"Error in temporary directory operation: {e}")
        raise
    finally:
        # Cleanup
        if tmp_dir and os.path.exists(tmp_dir):
            try:
                import shutil
                shutil.rmtree(tmp_dir)
                logger.debug(f"Cleaned up temporary directory: {tmp_dir}")
            except OSError as e:
                logger.warning(f"Failed to cleanup temporary directory {tmp_dir}: {e}")


class TempFileManager:
    """
    Manager for tracking and cleaning up temporary files.
    
    Useful when you need to manage multiple temporary files across
    different scopes and ensure they're all cleaned up.
    """
    
    def __init__(self):
        self._temp_files = set()
        self._temp_dirs = set()
    
    def create_temp_file(self, suffix: Optional[str] = None,
                        prefix: Optional[str] = None,
                        dir: Optional[str] = None) -> str:
        """
        Create a temporary file and track it for cleanup.
        
        Args:
            suffix: File suffix
            prefix: File prefix
            dir: Directory to create file in
            
        Returns:
            str: Path to the temporary file
        """
        fd, tmp_path = tempfile.mkstemp(suffix=suffix, prefix=prefix, dir=dir)
        os.close(fd)
        
        self._temp_files.add(tmp_path)
        logger.debug(f"Created tracked temporary file: {tmp_path}")
        return tmp_path
    
    def create_temp_dir(self, suffix: Optional[str] = None,
                       prefix: Optional[str] = None,
                       dir: Optional[str] = None) -> str:
        """
        Create a temporary directory and track it for cleanup.
        
        Args:
            suffix: Directory suffix
            prefix: Directory prefix
            dir: Parent directory
            
        Returns:
            str: Path to the temporary directory
        """
        tmp_dir = tempfile.mkdtemp(suffix=suffix, prefix=prefix, dir=dir)
        self._temp_dirs.add(tmp_dir)
        logger.debug(f"Created tracked temporary directory: {tmp_dir}")
        return tmp_dir
    
    def cleanup_file(self, file_path: str) -> bool:
        """
        Clean up a specific temporary file.
        
        Args:
            file_path: Path to the file to clean up
            
        Returns:
            bool: True if successfully cleaned up
        """
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
                logger.debug(f"Cleaned up temporary file: {file_path}")
            
            self._temp_files.discard(file_path)
            return True
        except OSError as e:
            logger.warning(f"Failed to cleanup temporary file {file_path}: {e}")
            return False
    
    def cleanup_dir(self, dir_path: str) -> bool:
        """
        Clean up a specific temporary directory.
        
        Args:
            dir_path: Path to the directory to clean up
            
        Returns:
            bool: True if successfully cleaned up
        """
        try:
            if os.path.exists(dir_path):
                import shutil
                shutil.rmtree(dir_path)
                logger.debug(f"Cleaned up temporary directory: {dir_path}")
            
            self._temp_dirs.discard(dir_path)
            return True
        except OSError as e:
            logger.warning(f"Failed to cleanup temporary directory {dir_path}: {e}")
            return False
    
    def cleanup_all(self) -> None:
        """Clean up all tracked temporary files and directories."""
        # Clean up files
        for file_path in list(self._temp_files):
            self.cleanup_file(file_path)
        
        # Clean up directories
        for dir_path in list(self._temp_dirs):
            self.cleanup_dir(dir_path)
        
        logger.info(f"Cleaned up all temporary files and directories")
    
    def __del__(self):
        """Cleanup on destruction."""
        if hasattr(self, '_temp_files') and hasattr(self, '_temp_dirs'):
            if self._temp_files or self._temp_dirs:
                logger.warning("TempFileManager destroyed with uncleaned temporary files/dirs")
                self.cleanup_all()


# Global temp file manager instance for convenience
_global_temp_manager = TempFileManager()


def create_temp_file(suffix: Optional[str] = None,
                    prefix: Optional[str] = None,
                    dir: Optional[str] = None) -> str:
    """
    Create a temporary file using the global manager.
    
    Note: Remember to call cleanup_temp_file() or use safe_temp_file() context manager.
    """
    return _global_temp_manager.create_temp_file(suffix, prefix, dir)


def cleanup_temp_file(file_path: str) -> bool:
    """Clean up a temporary file using the global manager."""
    return _global_temp_manager.cleanup_file(file_path)


def cleanup_all_temp_files() -> None:
    """Clean up all temporary files tracked by the global manager."""
    _global_temp_manager.cleanup_all()