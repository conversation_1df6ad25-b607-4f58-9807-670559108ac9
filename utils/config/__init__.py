"""
Configuration management for the document converter service.

This module provides centralized configuration management using ConfigParser.
Configuration can be loaded from environment variables or default files.
"""

import configparser
import logging
import os
from typing import Optional

logger = logging.getLogger(__name__)

# 获取当前文件所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 构建配置文件的完整路径，优先使用环境变量
default_config_path = os.path.join(
    os.path.dirname(os.path.dirname(current_dir)), "config", "config.ini"
)
config_path = os.getenv("CONFIG_FILE_PATH", default_config_path)

# 初始化全局配置
global_config = configparser.ConfigParser()


def load_config(config_file_path: Optional[str] = None) -> bool:
    """
    Load configuration from file.

    Args:
        config_file_path: Path to configuration file. If None, uses default path.

    Returns:
        bool: True if configuration loaded successfully, False otherwise
    """
    path = config_file_path or config_path

    if not os.path.exists(path):
        logger.error(f"Configuration file not found: {path}")
        return False

    try:
        global_config.read(path, encoding="utf-8")
        logger.info(f"Configuration loaded from: {path}")
        return True
    except Exception as e:
        logger.error(f"Failed to load configuration from {path}: {str(e)}")
        return False


def print_global_config() -> None:
    """Print all configuration sections and values."""
    logger.info("Current configuration:")
    for section in global_config.sections():
        logger.info(f"[{section}]")
        for key, value in global_config[section].items():
            # 隐藏敏感信息
            if any(
                sensitive in key.lower()
                for sensitive in ["password", "secret", "key", "token"]
            ):
                value = "*" * len(value)
            logger.info(f"  {key} = {value}")


def get_config_value(section: str, key: str, fallback: Optional[str] = None) -> str:
    """
    Get configuration value with fallback.

    Args:
        section: Configuration section name
        key: Configuration key name
        fallback: Fallback value if key not found

    Returns:
        str: Configuration value or fallback
    """
    try:
        return global_config.get(section, key, fallback=fallback)
    except (configparser.NoSectionError, configparser.NoOptionError):
        logger.warning(
            f"Configuration key [{section}].{key} not found, using fallback: {fallback}"
        )
        return fallback


# 加载配置
if not load_config():
    logger.warning("Using default configuration values")
