{"version": 1, "disable_existing_loggers": false, "formatters": {"json": {"()": "pythonjsonlogger.jsonlogger.JsonFormatter", "format": "%(asctime)s %(name)s %(levelname)s %(message)s"}}, "handlers": {"console": {"class": "logging.StreamHandler", "level": "DEBUG", "formatter": "json", "stream": "ext://sys.stdout"}, "serverfile": {"class": "logging.handlers.RotatingFileHandler", "level": "DEBUG", "formatter": "json", "filename": "./logs/server.log", "maxBytes": 104857600, "backupCount": 30, "encoding": "utf8"}, "enginefile": {"class": "logging.handlers.RotatingFileHandler", "level": "DEBUG", "formatter": "json", "filename": "./logs/engine.log", "maxBytes": 104857600, "backupCount": 30, "encoding": "utf8"}}, "loggers": {"serverlog": {"level": "INFO", "handlers": ["serverfile"], "propagate": false}, "enginelog": {"level": "INFO", "handlers": ["enginefile"], "propagate": false}}, "root": {"level": "INFO", "handlers": ["console"]}}