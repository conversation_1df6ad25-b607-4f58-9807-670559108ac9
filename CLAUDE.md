# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with this Document Converter Service repository.

## Development Commands

### Starting the Service
```bash
# Development mode (single process, auto-reload)
python main.py

# Using the enhanced startup script with environment checks and validation
python script/start_service.py --host 0.0.0.0 --port 8000 --reload
python script/start_service.py --check-only  # Environment validation only

# Production mode with multiple workers
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000

# Using shell script with command-line options (supports sidecar service)
./run.sh -p 8000 -w 1  # Includes skynet-mesh sidecar service

# Build and run with Docker
bash script/build.sh
```

### Testing
```bash
# Run all tests
python -m pytest test/

# Run specific test files
python test/test_config.py
python test/test_logger.py
python test/test_api.py
python test/test_integration.py
python test/test_elk.py
python test/test_stop.py

# Test service health
python script/test_service.py

# Project health check
python script/project_health_check.py
```

### Model Management
```bash
# Download required AI models
python script/download_models_hf.py

# Manual static file cleanup
python script/cleanup_static.py --max-age-days 7 --max-files 1000
python script/cleanup_static.py --dry-run  # Preview what would be deleted
python script/cleanup_static.py --verbose  # Enable detailed logging
```

## Required Environment Variables

Before running the service, ensure these environment variables are set:

```bash
export CONFIG_FILE_PATH=/path/to/doc-convert/config/config.ini
export MINERU_TOOLS_CONFIG_JSON=/path/to/doc-convert/config/magic-pdf.json
```

The startup script `script/start_service.py` will validate these are set properly and check for:
- Environment variable existence
- Configuration file accessibility  
- Required Python dependencies availability
- Service readiness validation

## Architecture Overview

This is a **FastAPI-based document conversion service** that supports multiple AI models for converting various document formats (PDF, images, Office documents) to Markdown.

### Core Components

1. **Multi-Model Architecture**: The service supports 4 different AI engines:
   - **Mineru**: PDF processing with OCR capabilities (magic-pdf-v2)
   - **Dolphin**: Advanced image document understanding and layout parsing
   - **Monkey**: Additional document processing model with OCR capabilities
   - **MarkItDown**: General-purpose document converter for Office files

2. **Dynamic Model Loading**: Models are loaded dynamically based on the `models` configuration in `config/config.ini`. Only enabled models are loaded into memory to optimize resource usage.

3. **Modular Router System**: Each AI model has its own API router in `server/api/`:
   - `mineru_api.py` - PDF processing endpoints (magic-pdf integration)
   - `dolphin_api.py` - Image document processing and layout analysis
   - `monkey_api.py` - Alternative document processing with monkey OCR
   - `markitdown_api.py` - Office document conversion (DOC, PPT, XLS, etc.)

4. **Unified API Interface**: All models expose the same REST API pattern:
   - `POST /api/{model}/v1/parse_file` - Upload file processing
   - `POST /api/{model}/v1/parse_url` - URL-based file processing

5. **Sidecar Architecture**: Optional skynet-mesh sidecar service for advanced networking and service mesh capabilities.

### Key Directories

- `server/` - FastAPI application and API routes
  - `api/common.py` - Shared models, file handling, validation
  - `api/*_api.py` - API routers for different models (mineru, dolphin, monkey, markitdown)
  - `start.py` - Router registration and model loading logic
  - `app_factory.py` - FastAPI application factory and configuration
- `engine/` - AI model wrapper classes and implementations
  - `dolphin.py` - Dolphin model interface
  - `mineru.py` - Mineru (magic-pdf) model interface
  - `monkey.py` - Monkey OCR model interface
  - `magic_pdf_v2/` - Complete magic-pdf v2 implementation
- `utils/` - Utility modules
  - `config/` - Configuration management
  - `dolphin/` - Dolphin model utilities (markdown_utils, model, processor, utils)
  - `graceful_stop/` - Graceful shutdown handling
  - `logger/` - Logging configuration
  - `route_printer.py` - Route printing utilities
  - `lifespan.py` - Application lifespan management
  - `static_cleanup.py` - Static file cleanup utilities
  - `temp_file_manager.py` - Temporary file management
- `config/` - Configuration files for service and models
  - `config.ini` - Main service configuration
  - `magic-pdf.json` - Mineru model settings
  - `model_configs.yaml` - Monkey model configuration
  - `side-car.toml` - Sidecar service settings
- `static/` - Generated output files served as static content
- `resource/` - AI model files and weights
  - `dolphin_model/` - Dolphin model weights
  - `mineru_model/` - Mineru model files and dependencies
  - `model_weight/` - Additional model weights
- `test/` - Test suites for different components
- `demo/` - Example files for testing (img, office, pdf)
- `docs/` - Project documentation (API.md, OPTIMIZATION_SUMMARY.md)
- `cicd/` - Docker and CI/CD configurations
- `script/` - Utility scripts (build, cleanup, download, health check, etc.)
- `vendor/` - Third-party dependencies
- `skynet-mesh` - Sidecar service binary

### Configuration System

The service uses a centralized configuration system (`utils/config/`) that loads settings from:
- `config/config.ini` - Main service configuration with sections for server, models, cleanup, upload limits
- `config/magic-pdf.json` - Mineru model settings and magic-pdf configuration
- `config/model_configs.yaml` - Monkey model configuration and parameters
- `config/side-car.toml` - Skynet-mesh sidecar service settings

### Key Configuration Sections

- `[version]` - Service version and description information
- `[server]` - Host, port, and server settings (default port: 11288)
- `[upload]` - File upload limits (100MB max) and allowed extensions
- `[cleanup]` - Static file cleanup settings (auto-cleanup on startup, configurable retention)
- `[elk]` - Logging configuration (JSON structured logging)
- `[models]` - Dynamic model loading configuration (currently supports: mineru, dolphin, monkey, markitdown)
- `[dolphin]` - Dolphin model specific settings (device, batch size, model path)
- `[mineru]` - Mineru model configuration path
- `[monkey]` - Monkey model configuration path

### Processing Pipeline

1. **File Input**: Files can be uploaded directly or processed via URL
2. **Validation**: File type, size, and format validation in `server/api/common.py`
3. **Model Processing**: Route to appropriate AI engine based on endpoint
4. **Output Generation**: Results saved to `static/` directory with unique timestamped folders
5. **Response**: Standardized JSON response with markdown content and image URLs

### Static File Management

The service includes **automatic file cleanup** to manage disk space usage:

#### Cleanup Configuration
- Configurable via `[cleanup]` section in `config/config.ini`
- `max_age_days`: Maximum age of files to keep (default: 7 days)
- `max_files`: Maximum number of result directories to keep (default: 1000)
- Automatic cleanup runs on service startup

#### Directory Structure
Processed files are stored in `static/` with timestamp-based directory structure:
```
static/
├── YYYYMMDDHHMMSS_uuid/
│   ├── uuid.md          # Generated markdown
│   └── images/          # Extracted images
│       ├── image1.jpg
│       └── image2.png
```

#### Manual Cleanup
Use the dedicated cleanup script for manual file management:
```bash
# Standard cleanup with default settings
python script/cleanup_static.py

# Custom cleanup parameters
python script/cleanup_static.py --max-age-days 3 --max-files 500

# Preview mode (no actual deletion)
python script/cleanup_static.py --dry-run

# Verbose output
python script/cleanup_static.py --verbose
```

### Error Handling and Logging

- Structured JSON logging to `logs/doc_convert.jsonl`
- Request tracing with trace IDs
- Standardized error responses via `server/api/common.py`
- Graceful shutdown handling in `utils/graceful_stop/`

## Important Notes

- **Resource Requirements**: The service requires significant computational resources for AI models (8GB+ RAM recommended)
- **GPU Support**: GPU acceleration is supported and recommended (CUDA for optimal performance)
- **Model Storage**: Model files are large (several GB) and stored in `resource/` directory
- **Default Configuration**: Default port is 11288 (configurable in config.ini), supports both HTTP and sidecar mesh
- **API Documentation**: 
  - Swagger UI: `/docs`
  - ReDoc: `/redoc` 
  - Health check: `/health`
  - Service info: `/`
- **File Management**: Automatic cleanup of old static files based on age and count limits
- **Supported Formats**: PDF, images (PNG, JPG, etc.), Office documents (DOC, PPT, XLS)
- **Development Tools**: Comprehensive test suite, project health checks, and development scripts
- **Deployment Options**: Direct Python execution, Gunicorn, Docker, or shell script with sidecar