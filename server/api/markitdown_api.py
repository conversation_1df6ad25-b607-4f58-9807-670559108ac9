"""
MarkItDown API for general document parsing.

This module provides API endpoints for parsing various document formats using MarkItDown.
MarkItDown supports Office documents, PDFs, images, and other common file formats.
"""

import logging
import os

from fastapi import APIRouter, File, HTTPException, Request, UploadFile
from markitdown import MarkItDown

from .common import *

logger = logging.getLogger(__name__)

markitdown_router = APIRouter(prefix="/markitdown/v1")


@markitdown_router.post("/parse_file", response_model=ParseResponse)
async def parse_file(file: UploadFile = File(...), request: Request = None):
    """
    Parse uploaded file using MarkItDown and convert to Markdown.

    Args:
        file: Uploaded file (supports various formats)
        request: FastAPI request object

    Returns:
        ParseResponse: Response containing parsed markdown and metadata
    """
    try:
        tmp_path = await save_upload_file_to_tmp(file)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"文件保存失败: {str(e)}")

    output_base, md_file_url, _, _, file_name = gen_output_dir_and_urls(request)
    os.makedirs(output_base, exist_ok=True)

    try:
        md = MarkItDown(enable_plugins=False)
        result = md.convert(tmp_path)
        md_content = result.text_content
        output_file_path = os.path.join(output_base, f"{file_name}.md")
        with open(output_file_path, "w", encoding="utf-8") as f:
            f.write(md_content)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"解析失败: {str(e)}")
    finally:
        os.unlink(tmp_path)

    return ParseResponse(
        header=ParseHeader(),
        payload=ParseRespPayload(file=md_file_url, markdown=md_content),
    )


@markitdown_router.post("/parse_url", response_model=ParseResponse)
async def parse_url(request: Request, body: ParseRequest):
    """
    Parse file from URL using MarkItDown and convert to Markdown.

    Args:
        request: FastAPI request object
        body: Request body containing file URL

    Returns:
        ParseResponse: Response containing parsed markdown and metadata
    """
    try:
        file_url = body.payload.file_url
        if not file_url:
            raise HTTPException(
                status_code=400, detail="request payload must contain 'file_url'"
            )
        tmp_path = await download_file_to_tmp(file_url)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"文件下载失败: {str(e)}")

    output_base, md_file_url, _, _, file_name = gen_output_dir_and_urls(request)
    os.makedirs(output_base, exist_ok=True)

    try:
        md = MarkItDown(enable_plugins=False)
        result = md.convert(tmp_path)
        md_content = result.text_content
        output_file_path = os.path.join(output_base, f"{file_name}.md")
        with open(output_file_path, "w", encoding="utf-8") as f:
            f.write(md_content)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"解析失败: {str(e)}")
    finally:
        os.unlink(tmp_path)

    return ParseResponse(
        header=ParseHeader(traceId=body.header.traceId),
        payload=ParseRespPayload(file=md_file_url, markdown=md_content),
    )
