"""
MonkeyOCR API for PDF document parsing.

This module provides API endpoints for parsing PDF documents using the MonkeyOCR model.
MonkeyOCR specializes in extracting structured content from PDF documents including text,
images, tables, and formulas.
"""

import logging
import os

from fastapi import APIRouter, File, HTTPException, Request, UploadFile

from engine.monkey import get_monkey_engine
from server.api.common import ParseResponse
from utils.config import global_config

from .common import *

logger = logging.getLogger(__name__)

monkey_router = APIRouter(prefix="/monkey/v1")
monkey_engine = get_monkey_engine(global_config.get("monkey", "config_path"))


@monkey_router.post("/parse_file", response_model=ParseResponse)
async def parse_file(
    file: UploadFile = File(...),
    request: Request = None,
) -> ParseResponse:
    """
    Parse uploaded PDF file and convert to Markdown using MonkeyOCR.

    Args:
        file: Uploaded PDF file
        request: FastAPI request object

    Returns:
        ParseResponse: Response containing parsed markdown and metadata
    """
    try:
        tmp_path = await save_upload_file_to_tmp(file)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"文件保存失败: {str(e)}")

    output_base, md_url_file, dir_name, base_url, file_name = gen_output_dir_and_urls(
        request
    )
    image_url_prefix = f"{base_url}/static/{dir_name}/images"

    try:
        md_content, image_files = monkey_engine.parse(
            tmp_path, output_base, image_url_prefix, file_name=file_name
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"PDF解析失败: {str(e)}")
    finally:
        os.unlink(tmp_path)

    return ParseResponse(
        header=ParseHeader(),
        payload=ParseRespPayload(
            markdown=md_content, images=image_files, file=md_url_file
        ),
    )


@monkey_router.post("/parse_url", response_model=ParseResponse)
async def parse_url(
    request: Request,
    body: ParseRequest,
) -> ParseResponse:
    """
    Parse PDF file from URL and convert to Markdown using MonkeyOCR.

    Args:
        request: FastAPI request object
        body: Request body containing file URL

    Returns:
        ParseResponse: Response containing parsed markdown and metadata
    """
    try:
        file_url = body.payload.file_url
        if not file_url:
            raise HTTPException(
                status_code=400, detail="request payload must contain 'file_url'"
            )
        tmp_path = await download_file_to_tmp(file_url)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"文件下载失败: {str(e)}")

    output_base, md_url_file, dir_name, base_url, file_name = gen_output_dir_and_urls(
        request
    )
    image_url_prefix = f"{base_url}/static/{dir_name}/images"

    try:
        md_content, image_files = monkey_engine.parse(
            tmp_path, output_base, image_url_prefix, file_name=file_name
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"PDF解析失败: {str(e)}")
    finally:
        os.unlink(tmp_path)

    return ParseResponse(
        header=ParseHeader(traceId=body.header.traceId),
        payload=ParseRespPayload(
            markdown=md_content, images=image_files, file=md_url_file
        ),
    )
