"""
Common utilities and models for the document converter API.

This module provides shared functionality including:
- API request/response models
- File handling utilities
- Error handling utilities
- Common constants
"""

import datetime
import logging
import os
import tempfile
import uuid
from typing import List, Optional, Tuple

import httpx
from fastapi import HTTPException, Request, UploadFile
from pydantic import BaseModel, Field

from utils.config import global_config

logger = logging.getLogger(__name__)

# Constants - these will be initialized from config
STATIC_RES_DIR = "static"
STATIC_IMG_DIR = "images"

# Get file upload limits from config
MAX_FILE_SIZE = (
    global_config.getint("upload", "max_file_size_mb", fallback=100) * 1024 * 1024
)

# Get allowed extensions from config
allowed_extensions_str = global_config.get(
    "upload",
    "allowed_extensions",
    fallback="pdf,png,jpg,jpeg,gif,bmp,tiff,webp,doc,docx,ppt,pptx,xls,xlsx",
)
ALLOWED_EXTENSIONS = {ext.strip().lower() for ext in allowed_extensions_str.split(",")}

logger.info(
    f"File upload limits: {MAX_FILE_SIZE // (1024*1024)}MB, extensions: {ALLOWED_EXTENSIONS}"
)


class ParseHeader(BaseModel):
    """Standard API response header."""

    code: int = Field(default=0, description="Response code, 0 for success")
    success: str = Field(default="success", description="Response status")
    traceId: str = Field(default="", description="Trace ID for request tracking")
    skynet_tlb_service_tag_selector: str = Field(
        default="", description="Service tag selector"
    )


class ParseReqPayload(BaseModel):
    """Request payload for URL-based parsing."""

    file_url: str = Field(description="URL of the file to be parsed")


class ParseRespPayload(BaseModel):
    """Response payload containing parsed results."""

    markdown: str = Field(description="Parsed markdown content")
    images: List[str] = Field(default=[], description="List of extracted image URLs")
    file: str = Field(description="URL of the output file")


class ParseRequest(BaseModel):
    """Standard API request model."""

    header: ParseHeader
    payload: ParseReqPayload


class ParseResponse(BaseModel):
    """Standard API response model."""

    header: ParseHeader
    payload: ParseRespPayload


class ErrorResponse(BaseModel):
    """Error response model."""

    code: int = Field(description="Error code")
    message: str = Field(description="Error message")
    detail: Optional[str] = Field(
        default=None, description="Detailed error information"
    )
    traceId: Optional[str] = Field(
        default=None, description="Trace ID for error tracking"
    )


# Utility functions


def validate_file_extension(filename: str) -> bool:
    """
    Validate if the file extension is allowed.

    Args:
        filename: Name of the file to validate

    Returns:
        bool: True if extension is allowed, False otherwise
    """
    if not filename:
        return False
    extension = os.path.splitext(filename)[-1].lower().lstrip(".")
    return extension in ALLOWED_EXTENSIONS


def validate_file_size(file_size: int) -> bool:
    """
    Validate if the file size is within allowed limits.

    Args:
        file_size: Size of the file in bytes

    Returns:
        bool: True if size is allowed, False otherwise
    """
    return 0 < file_size <= MAX_FILE_SIZE


async def save_upload_file_to_tmp(upload_file: UploadFile) -> str:
    """
    Save uploaded file to temporary location with validation.

    Args:
        upload_file: FastAPI UploadFile object

    Returns:
        str: Path to the temporary file

    Raises:
        HTTPException: If file validation fails
    """
    if not upload_file.filename:
        raise HTTPException(status_code=400, detail="文件名不能为空")

    if not validate_file_extension(upload_file.filename):
        raise HTTPException(
            status_code=400,
            detail=f"不支持的文件格式。支持的格式: {', '.join(ALLOWED_EXTENSIONS)}",
        )

    # Read file content to check size
    content = await upload_file.read()
    if not validate_file_size(len(content)):
        raise HTTPException(
            status_code=413,
            detail=f"文件大小超过限制。最大允许大小: {MAX_FILE_SIZE // (1024*1024)}MB",
        )

    # Reset file pointer
    await upload_file.seek(0)

    suffix = os.path.splitext(upload_file.filename)[-1]

    # Create temporary file with proper cleanup handling
    fd, tmp_path = tempfile.mkstemp(suffix=suffix)
    try:
        with os.fdopen(fd, "wb") as tmp_file:
            # Copy file content
            content = await upload_file.read()
            tmp_file.write(content)

        logger.info(f"File saved to temporary location: {tmp_path}")
        return tmp_path
    except (OSError, IOError) as e:
        # Cleanup on error
        try:
            os.unlink(tmp_path)
        except OSError:
            pass
        logger.error(f"File I/O error while saving upload file: {str(e)}")
        raise HTTPException(status_code=500, detail="文件保存失败")
    except Exception as e:
        # Cleanup on error
        try:
            os.unlink(tmp_path)
        except OSError:
            pass
        logger.error(f"Unexpected error while saving upload file: {str(e)}")
        raise HTTPException(status_code=500, detail="文件保存失败")
    finally:
        await upload_file.seek(0)  # Reset for any further use


async def download_file_to_tmp(file_url: str) -> str:
    """
    Download file from URL to temporary location with validation.

    Args:
        file_url: URL of the file to download

    Returns:
        str: Path to the temporary file

    Raises:
        HTTPException: If download or validation fails
    """
    if not file_url:
        raise HTTPException(status_code=400, detail="文件URL不能为空")

    # Basic URL validation for security
    if not file_url.startswith(("http://", "https://")):
        raise HTTPException(status_code=400, detail="只支持HTTP/HTTPS协议的URL")

    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            resp = await client.get(file_url, follow_redirects=True)
            resp.raise_for_status()

            # Validate file size
            content_length = resp.headers.get("content-length")
            if content_length and not validate_file_size(int(content_length)):
                raise HTTPException(
                    status_code=413,
                    detail=f"文件大小超过限制。最大允许大小: {MAX_FILE_SIZE // (1024*1024)}MB",
                )

            # Sanitize filename from URL
            filename = file_url.split("/")[-1] or "file"
            # Remove dangerous characters from filename
            import re

            filename = re.sub(r'[<>:"/\\|?*]', "_", filename)

            if not validate_file_extension(filename):
                raise HTTPException(
                    status_code=400,
                    detail=f"不支持的文件格式。支持的格式: {', '.join(ALLOWED_EXTENSIONS)}",
                )

            suffix = os.path.splitext(filename)[-1]

            # Create temporary file with proper cleanup handling
            fd, tmp_path = tempfile.mkstemp(suffix=suffix)
            try:
                with os.fdopen(fd, "wb") as tmp_file:
                    tmp_file.write(resp.content)

                logger.info(f"File downloaded to temporary location: {tmp_path}")
                return tmp_path
            except (OSError, IOError) as e:
                # Cleanup on error
                try:
                    os.unlink(tmp_path)
                except OSError:
                    pass
                logger.error(f"File I/O error while downloading: {str(e)}")
                raise HTTPException(status_code=500, detail="文件下载失败")
            except Exception as e:
                # Cleanup on error
                try:
                    os.unlink(tmp_path)
                except OSError:
                    pass
                raise e

    except httpx.HTTPError as e:
        logger.error(f"Failed to download file from {file_url}: {str(e)}")
        raise HTTPException(status_code=400, detail=f"文件下载失败: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error downloading file: {str(e)}")
        raise HTTPException(status_code=500, detail="文件下载失败")


def gen_output_dir_and_urls(
    request: Request, static_dir: str = STATIC_RES_DIR
) -> Tuple[str, str, str, str, str]:
    """
    Generate output directory and URLs for processed files.

    Args:
        request: FastAPI Request object
        static_dir: Static directory name

    Returns:
        Tuple containing:
        - output_base: Local output directory path
        - md_file_url: URL to the markdown file
        - dir_name: Generated directory name
        - base_url: Base URL of the service
        - file_name: Generated file name
    """
    now_str = datetime.datetime.now().strftime("%Y%m%d%H%M%S%f")[:-3]
    uuid_str = uuid.uuid4().hex[:8]
    dir_name = f"{now_str}_{uuid_str}"
    output_base = os.path.join(static_dir, dir_name)
    base_url = str(request.base_url).rstrip("/")
    md_file_url = f"{base_url}/static/{dir_name}/{uuid_str}.md"
    return output_base, md_file_url, dir_name, base_url, uuid_str


def create_error_response(
    code: int,
    message: str,
    detail: Optional[str] = None,
    trace_id: Optional[str] = None,
) -> ErrorResponse:
    """
    Create standardized error response.

    Args:
        code: Error code
        message: Error message
        detail: Detailed error information
        trace_id: Trace ID for error tracking

    Returns:
        ErrorResponse: Standardized error response
    """
    return ErrorResponse(code=code, message=message, detail=detail, traceId=trace_id)
