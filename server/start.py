from utils.config import global_config

MODEL_DOLPHIN = "dolphin"
MODEL_MINERU = "mineru"
MODEL_MONKEY = "monkey"
MODEL_MARKITDOWN = "markitdown"


def load_routers() -> dict:
    routers = {}
    models = global_config.get("version", "models")
    models = [model for model in models.split(",") if model.strip()]
    for model in models:
        if model == MODEL_DOLPHIN:
            from server.api.dolphin_api import dolphin_router

            routers[MODEL_DOLPHIN] = {
                "router": dolphin_router,
                "tag": model,
                "prefix": "/api",
            }

        elif model == MODEL_MINERU:
            from server.api.mineru_api import mineru_router

            routers[MODEL_MINERU] = {
                "router": mineru_router,
                "tag": model,
                "prefix": "/api",
            }

        elif model == MODEL_MONKEY:
            from server.api.monkey_api import monkey_router

            routers[MODEL_MONKEY] = {
                "router": monkey_router,
                "tag": model,
                "prefix": "/api",
            }

        elif model == MODEL_MARKITDOWN:
            from server.api.markitdown_api import markitdown_router

            routers[MODEL_MARKITDOWN] = {
                "router": markitdown_router,
                "tag": model,
                "prefix": "/api",
            }

    return routers
