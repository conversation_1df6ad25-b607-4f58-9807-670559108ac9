"""
FastAPI application factory for the Document Converter Service.

This module contains the application creation and configuration logic.
"""

import logging

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles

from server.api.common import STATIC_RES_DIR
from server.start import load_routers
from utils.config import global_config
from utils.lifespan import lifespan
from vendor.pandora import elk
from vendor.pandora.middleware.fastapi_middle import ContextMiddleware

logger = logging.getLogger(__name__)


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Returns:
        FastAPI: Configured FastAPI application instance
    """
    # 创建FastAPI应用实例
    app = _create_fastapi_instance()

    # 添加中间件
    _add_middleware(app)

    # 初始化日志系统
    _init_logging()

    # 挂载静态文件
    _mount_static_files(app)

    # 注册API路由
    _register_api_routes(app)

    # 添加系统端点
    _add_system_endpoints(app)

    return app


def _create_fastapi_instance() -> FastAPI:
    """
    Create the FastAPI instance with basic configuration.

    Returns:
        FastAPI: Basic FastAPI instance
    """
    return FastAPI(
        title=global_config.get("version", "title"),
        description=global_config.get("version", "description"),
        version=global_config.get("version", "version"),
        lifespan=lifespan,
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
    )


def _add_middleware(app: FastAPI) -> None:
    """
    Add middleware to the FastAPI application.

    Args:
        app: FastAPI application instance
    """
    app.add_middleware(ContextMiddleware)


def _init_logging() -> None:
    """
    Initialize the global logging system.
    """
    elk.init_global_logger(
        name=global_config.get("elk", "name"),
        log_path=global_config.get("elk", "log_path"),
        level=global_config.get("elk", "level"),
    )


def _mount_static_files(app: FastAPI) -> None:
    """
    Mount static file directories.

    Args:
        app: FastAPI application instance
    """
    app.mount(
        "/static",
        StaticFiles(directory=STATIC_RES_DIR),
        name="static",
    )


def _register_api_routes(app: FastAPI) -> None:
    """
    Register API routes for all enabled models.

    Args:
        app: FastAPI application instance
    """
    routers = load_routers()
    for model, config in routers.items():
        app.include_router(
            config["router"],
            prefix=config.get("prefix", "/api"),
            tags=[config["tag"]],
        )
        logger.info(f"Registered router for model: {model}")

    # Store routers in app state for later access
    app.state.routers = routers


def _add_system_endpoints(app: FastAPI) -> None:
    """
    Add system endpoints like health check, root, and models info.

    Args:
        app: FastAPI application instance
    """

    @app.get("/health", tags=["Health"])
    async def health_check():
        """健康检查端点"""
        return {"status": "healthy", "service": "document-converter"}

    @app.get("/", tags=["Root"])
    async def root():
        """根端点，返回服务信息"""
        return {
            "service": global_config.get("version", "title"),
            "version": global_config.get("version", "version"),
            "description": global_config.get("version", "description"),
            "health": "/health",
        }

    @app.get("/models", tags=["Models"])
    async def get_available_models():
        """获取可用的模型列表"""
        routers = getattr(app.state, "routers", {})
        return {"available_models": list(routers.keys())}
