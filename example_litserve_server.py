#!/usr/bin/env python3
"""
Example LitServe server for document conversion.

This script demonstrates how to start individual LitServe servers for each document processing model.
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def start_mineru_server(port: int = 11288, device: str = "auto"):
    """Start Mineru LitServe server."""
    try:
        import litserve as ls
        from litserve_apis import MineruAPI
        
        logger.info(f"Starting Mineru LitServe on port {port} with device {device}")
        
        api = MineruAPI()
        server = ls.LitServer(
            api,
            accelerator=device,
            workers_per_device=1,
            max_batch_size=1
        )
        
        logger.info("Mineru server starting...")
        server.run(port=port)
        
    except Exception as e:
        logger.error(f"Failed to start Mineru server: {str(e)}")
        raise


def start_dolphin_server(port: int = 11289, device: str = "auto"):
    """Start Dolphin LitServe server."""
    try:
        import litserve as ls
        from litserve_apis import DolphinAPI
        
        logger.info(f"Starting Dolphin LitServe on port {port} with device {device}")
        
        api = DolphinAPI()
        server = ls.LitServer(
            api,
            accelerator=device,
            workers_per_device=2,
            max_batch_size=8,
            batch_timeout=1.0
        )
        
        logger.info("Dolphin server starting...")
        server.run(port=port)
        
    except Exception as e:
        logger.error(f"Failed to start Dolphin server: {str(e)}")
        raise


def start_monkey_server(port: int = 11290, device: str = "auto"):
    """Start Monkey OCR LitServe server."""
    try:
        import litserve as ls
        from litserve_apis import MonkeyAPI
        
        logger.info(f"Starting Monkey OCR LitServe on port {port} with device {device}")
        
        api = MonkeyAPI()
        server = ls.LitServer(
            api,
            accelerator=device,
            workers_per_device=1,
            max_batch_size=4
        )
        
        logger.info("Monkey OCR server starting...")
        server.run(port=port)
        
    except Exception as e:
        logger.error(f"Failed to start Monkey OCR server: {str(e)}")
        raise


def start_markitdown_server(port: int = 11291, device: str = "cpu"):
    """Start MarkItDown LitServe server."""
    try:
        import litserve as ls
        from litserve_apis import MarkItDownAPI
        
        logger.info(f"Starting MarkItDown LitServe on port {port} with device {device}")
        
        api = MarkItDownAPI()
        server = ls.LitServer(
            api,
            accelerator=device,
            workers_per_device=2
        )
        
        logger.info("MarkItDown server starting...")
        server.run(port=port)
        
    except Exception as e:
        logger.error(f"Failed to start MarkItDown server: {str(e)}")
        raise


def main():
    """Main function to parse arguments and start the appropriate server."""
    parser = argparse.ArgumentParser(description="Start LitServe document conversion servers")
    parser.add_argument(
        "--model", 
        choices=["mineru", "dolphin", "monkey", "markitdown"],
        required=True,
        help="Model type to start"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        help="Port to run the server on (default varies by model)"
    )
    parser.add_argument(
        "--device", 
        default="auto",
        help="Device to run the model on (default: auto)"
    )
    
    args = parser.parse_args()
    
    # Set default ports if not specified
    default_ports = {
        "mineru": 11288,
        "dolphin": 11289,
        "monkey": 11290,
        "markitdown": 11291
    }
    
    port = args.port or default_ports[args.model]
    
    # Start the appropriate server
    try:
        if args.model == "mineru":
            start_mineru_server(port, args.device)
        elif args.model == "dolphin":
            start_dolphin_server(port, args.device)
        elif args.model == "monkey":
            start_monkey_server(port, args.device)
        elif args.model == "markitdown":
            start_markitdown_server(port, args.device)
            
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
