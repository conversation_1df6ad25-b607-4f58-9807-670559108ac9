#!/bin/bash

# 默认值（可选）
PORT=18117
WORKERS=1

# 解析命令行参数
while getopts "p:w:" opt; do
  case $opt in
    p) PORT=$OPTARG ;;
    w) WORKERS=$OPTARG ;;
    *) echo "用法: $0 -p <端口号> -w <workers数量>"; exit 1 ;;
  esac
done

uvicorn main:app --host 0.0.0.0 --port $PORT --workers $WORKERS > python.log 2>&1 &
PY_PID=$!
./skynet-mesh server -c ./config/side-car.toml &
SK_PID=$!

trap "kill $SK_PID; exit" SIGINT SIGTERM

wait $PY_PID
kill $SK_PID