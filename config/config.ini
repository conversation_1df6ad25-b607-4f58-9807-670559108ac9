[version]
version = 1.0.0
title = Document Converter Service
description = A FastAPI-based service for converting various document formats (PDF, images, Office documents) to Markdown using multiple AI models
models = mineru,dolphin,monkey,markitdown

[server]
host = 0.0.0.0
port = 11288
name = doc-convert-server

[elk]
name = doc-convert
level = INFO
log_path = ./logs/doc_convert.jsonl

[cleanup]
# Static file cleanup settings
max_age_days = 7
max_files = 1000

[upload]
# File upload limits
max_file_size_mb = 100
allowed_extensions = pdf,png,jpg,jpeg,gif,bmp,tiff,webp,doc,docx,ppt,pptx,xls,xlsx

[dolphin]
model_path = ./resource/dolphin_model
device = cuda:0
max_batch_size = 8

[mineru]
config_path = ./config/magic-pdf.json

[monkey]
config_path = ./config/model_configs.yaml