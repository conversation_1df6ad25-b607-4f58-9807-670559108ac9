"""
MineruAPI - LitAPI implementation for PDF document parsing using Mineru engine.

This module provides a LitAPI implementation specifically for processing PDF documents
using the Mineru (magic-pdf) engine with advanced OCR capabilities.
"""

import logging
import os
from typing import Any, Dict

from .base import BaseDocumentAPI
from engine.mineru import get_mineru_engine


class MineruAPI(BaseDocumentAPI):
    """
    LitAPI implementation for PDF document parsing using Mineru engine.
    
    This API specializes in processing PDF documents with advanced OCR capabilities,
    extracting structured content including text, images, tables, and formulas.
    """
    
    def __init__(self):
        """Initialize MineruAPI."""
        super().__init__("mineru")
        self.engine = None
        
    def setup(self, device: str) -> None:
        """
        Initialize the Mineru engine.
        
        Args:
            device: Device to run the model on (e.g., 'cuda:0', 'cpu')
        """
        try:
            self.logger.info(f"Initializing Mineru engine on device: {device}")
            self.engine = get_mineru_engine()
            self.device = device
            self.logger.info("Mineru engine initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Mineru engine: {str(e)}")
            raise RuntimeError(f"Mineru engine initialization failed: {str(e)}")
    
    def predict(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute PDF document parsing using Mineru engine.
        
        Args:
            inputs: Decoded request inputs containing file information
            
        Returns:
            Dict containing parsing results with markdown content and images
        """
        file_path = None
        
        try:
            # Validate engine is initialized
            if self.engine is None:
                raise RuntimeError("Mineru engine not initialized")
            
            # Process file and generate output paths
            file_path, output_base, md_url, dir_name, base_url, file_name = self._handle_file_processing(inputs)
            
            # Create output directories
            os.makedirs(output_base, exist_ok=True)
            image_url_prefix = f"{base_url}/static/{dir_name}/images"
            
            self.logger.info(f"Processing PDF file: {file_path}")
            self.logger.info(f"Output directory: {output_base}")
            
            # Execute PDF parsing with Mineru engine
            md_content, image_files = self.engine.parse(
                file_path=file_path,
                output_dir=output_base,
                image_url_prefix=image_url_prefix
            )
            
            self.logger.info(f"PDF parsing completed successfully")
            self.logger.info(f"Generated {len(image_files)} image files")
            
            return {
                'markdown': md_content,
                'images': image_files,
                'file': md_url,
                'trace_id': inputs.get('trace_id', '')
            }
            
        except Exception as e:
            self.logger.error(f"PDF parsing failed: {str(e)}")
            return self._create_error_response(
                code=500,
                message="PDF parsing failed",
                detail=str(e),
                trace_id=inputs.get('trace_id', '')
            )
            
        finally:
            # Clean up temporary file
            if file_path:
                self._cleanup_temp_file(file_path)
    
    def decode_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Decode and validate PDF processing request.
        
        Args:
            request: Raw request data
            
        Returns:
            Dict containing parsed request data
        """
        try:
            # Use base class validation with PDF-specific checks
            decoded = super().decode_request(request)
            
            # Add PDF-specific validation if needed
            self._validate_pdf_request(decoded)
            
            return decoded
            
        except Exception as e:
            self.logger.error(f"PDF request decoding failed: {str(e)}")
            raise ValueError(f"Invalid PDF processing request: {str(e)}")
    
    def _validate_pdf_request(self, decoded_request: Dict[str, Any]) -> None:
        """
        Validate PDF-specific request parameters.
        
        Args:
            decoded_request: Decoded request data
            
        Raises:
            ValueError: If PDF-specific validation fails
        """
        # Check if file type is supported for PDF processing
        if decoded_request['type'] == 'url_download':
            file_url = decoded_request['file_url']
            if not any(file_url.lower().endswith(ext) for ext in ['.pdf']):
                self.logger.warning(f"File URL may not be a PDF: {file_url}")
        
        # Add any other PDF-specific validations here
        options = decoded_request.get('options', {})
        
        # Validate OCR options if provided
        if 'ocr_enabled' in options:
            if not isinstance(options['ocr_enabled'], bool):
                raise ValueError("ocr_enabled option must be a boolean")
        
        # Validate language options if provided
        if 'language' in options:
            supported_languages = ['auto', 'en', 'ch', 'ja', 'ko']
            if options['language'] not in supported_languages:
                raise ValueError(f"Unsupported language. Supported: {supported_languages}")
        
        self.logger.debug("PDF request validation passed")


# Factory function for easy instantiation
def create_mineru_api() -> MineruAPI:
    """
    Factory function to create a MineruAPI instance.
    
    Returns:
        MineruAPI: Configured MineruAPI instance
    """
    return MineruAPI()
