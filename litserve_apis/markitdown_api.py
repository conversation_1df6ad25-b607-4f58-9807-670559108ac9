"""
MarkItDownAPI - LitAPI implementation for Office document conversion using MarkItDown engine.

This module provides a LitAPI implementation specifically for converting Office documents
(DOC, DOCX, PPT, PPTX, XLS, XLSX) to Markdown using the MarkItDown engine.
"""

import logging
import os
from typing import Any, Dict

from .base import BaseDocumentAPI


class MarkItDownAPI(BaseDocumentAPI):
    """
    LitAPI implementation for Office document conversion using MarkItDown engine.
    
    This API specializes in converting various Office document formats to Markdown,
    supporting DOC, DOCX, PPT, PPTX, XLS, XLSX and other document formats.
    """
    
    def __init__(self):
        """Initialize MarkItDownAPI."""
        super().__init__("markitdown")
        self.engine = None
        
    def setup(self, device: str) -> None:
        """
        Initialize the MarkItDown engine.
        
        Args:
            device: Device to run the conversion on (typically 'cpu' for MarkItDown)
        """
        try:
            self.logger.info(f"Initializing MarkItDown engine on device: {device}")
            
            # MarkItDown typically runs on CPU
            self.device = device if device != 'auto' else 'cpu'
            
            # Initialize MarkItDown engine
            self.engine = self._create_markitdown_engine()
            
            self.logger.info("MarkItDown engine initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize MarkItDown engine: {str(e)}")
            raise RuntimeError(f"MarkItDown engine initialization failed: {str(e)}")
    
    def predict(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute Office document conversion using MarkItDown engine.
        
        Args:
            inputs: Decoded request inputs containing document information
            
        Returns:
            Dict containing conversion results with markdown content
        """
        file_path = None
        
        try:
            # Validate engine is initialized
            if self.engine is None:
                raise RuntimeError("MarkItDown engine not initialized")
            
            # Process file and generate output paths
            file_path, output_base, md_url, dir_name, base_url, file_name = self._handle_file_processing(inputs)
            
            # Create output directories
            os.makedirs(output_base, exist_ok=True)
            
            self.logger.info(f"Converting Office document: {file_path}")
            self.logger.info(f"Output directory: {output_base}")
            
            # Execute document conversion with MarkItDown
            md_content = self._convert_document(file_path, output_base, file_name)
            
            # Save markdown content to file
            md_file_path = os.path.join(output_base, f"{file_name}.md")
            with open(md_file_path, 'w', encoding='utf-8') as f:
                f.write(md_content)
            
            # Collect any generated image files
            image_files = self._collect_generated_images(output_base, base_url, dir_name)
            
            self.logger.info(f"Office document conversion completed successfully")
            self.logger.info(f"Generated {len(image_files)} image files")
            
            return {
                'markdown': md_content,
                'images': image_files,
                'file': md_url,
                'trace_id': inputs.get('trace_id', '')
            }
            
        except Exception as e:
            self.logger.error(f"Office document conversion failed: {str(e)}")
            return self._create_error_response(
                code=500,
                message="Office document conversion failed",
                detail=str(e),
                trace_id=inputs.get('trace_id', '')
            )
            
        finally:
            # Clean up temporary file
            if file_path:
                self._cleanup_temp_file(file_path)
    
    def decode_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Decode and validate Office document conversion request.
        
        Args:
            request: Raw request data
            
        Returns:
            Dict containing parsed request data
        """
        try:
            # Use base class validation with Office document-specific checks
            decoded = super().decode_request(request)
            
            # Add Office document-specific validation
            self._validate_office_request(decoded)
            
            return decoded
            
        except Exception as e:
            self.logger.error(f"Office document request decoding failed: {str(e)}")
            raise ValueError(f"Invalid Office document conversion request: {str(e)}")
    
    def _validate_office_request(self, decoded_request: Dict[str, Any]) -> None:
        """
        Validate Office document-specific request parameters.
        
        Args:
            decoded_request: Decoded request data
            
        Raises:
            ValueError: If Office document-specific validation fails
        """
        # Check if file type is supported for Office document conversion
        if decoded_request['type'] == 'url_download':
            file_url = decoded_request['file_url']
            office_extensions = ['.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx', '.pdf']
            if not any(file_url.lower().endswith(ext) for ext in office_extensions):
                self.logger.warning(f"File URL may not be an Office document: {file_url}")
        
        # Validate conversion options
        options = decoded_request.get('options', {})
        
        # Validate output format options
        if 'output_format' in options:
            supported_formats = ['markdown', 'md']
            if options['output_format'] not in supported_formats:
                raise ValueError(f"Unsupported output format. Supported: {supported_formats}")
        
        # Validate image extraction options
        if 'extract_images' in options:
            if not isinstance(options['extract_images'], bool):
                raise ValueError("extract_images option must be a boolean")
        
        # Validate table conversion options
        if 'preserve_tables' in options:
            if not isinstance(options['preserve_tables'], bool):
                raise ValueError("preserve_tables option must be a boolean")
        
        self.logger.debug("Office document request validation passed")
    
    def _create_markitdown_engine(self):
        """
        Create and configure the MarkItDown engine.
        
        Returns:
            MarkItDown engine instance
        """
        try:
            from markitdown import MarkItDown
            
            # Create MarkItDown instance with default configuration
            engine = MarkItDown()
            
            return engine
            
        except ImportError as e:
            self.logger.error("MarkItDown package not installed")
            raise RuntimeError("MarkItDown package not available. Please install with: pip install markitdown")
        except Exception as e:
            self.logger.error(f"Failed to create MarkItDown engine: {str(e)}")
            raise RuntimeError(f"MarkItDown engine creation failed: {str(e)}")
    
    def _convert_document(self, file_path: str, output_dir: str, file_name: str) -> str:
        """
        Convert document to Markdown using MarkItDown.
        
        Args:
            file_path: Path to input document
            output_dir: Output directory for converted files
            file_name: Base name for output files
            
        Returns:
            str: Converted Markdown content
        """
        try:
            # Convert document using MarkItDown
            result = self.engine.convert(file_path)
            
            if result and hasattr(result, 'text_content'):
                md_content = result.text_content
            else:
                # Fallback for different MarkItDown versions
                md_content = str(result) if result else ""
            
            if not md_content.strip():
                raise ValueError("MarkItDown conversion produced empty content")
            
            self.logger.debug(f"Document converted successfully, content length: {len(md_content)}")
            return md_content
            
        except Exception as e:
            self.logger.error(f"Document conversion failed: {str(e)}")
            raise RuntimeError(f"MarkItDown conversion failed: {str(e)}")
    
    def _collect_generated_images(self, output_base: str, base_url: str, dir_name: str) -> list:
        """
        Collect URLs of generated image files.
        
        Args:
            output_base: Base output directory
            base_url: Base URL for static files
            dir_name: Directory name for this processing session
            
        Returns:
            List of image file URLs
        """
        image_files = []
        
        try:
            # Check for images in the output directory
            if os.path.exists(output_base):
                for root, dirs, files in os.walk(output_base):
                    for file in files:
                        if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
                            # Generate relative path from output_base
                            rel_path = os.path.relpath(os.path.join(root, file), output_base)
                            image_url = f"{base_url}/static/{dir_name}/{rel_path}"
                            image_files.append(image_url)
            
            self.logger.debug(f"Collected {len(image_files)} generated image files")
            
        except Exception as e:
            self.logger.warning(f"Failed to collect generated images: {str(e)}")
        
        return image_files


# Factory function for easy instantiation
def create_markitdown_api() -> MarkItDownAPI:
    """
    Factory function to create a MarkItDownAPI instance.
    
    Returns:
        MarkItDownAPI: Configured MarkItDownAPI instance
    """
    return MarkItDownAPI()
