"""
MonkeyAPI - LitAPI implementation for OCR document recognition using Monkey engine.

This module provides a LitAPI implementation specifically for OCR document processing
using the Monkey OCR engine with multilingual text recognition capabilities.
"""

import logging
import os
from typing import Any, Dict

from .base import BaseDocumentAPI
from engine.monkey import get_monkey_engine
from utils.config import global_config


class MonkeyAPI(BaseDocumentAPI):
    """
    LitAPI implementation for OCR document recognition using Monkey engine.
    
    This API specializes in OCR document processing with multilingual text recognition,
    supporting both PDF and image document formats.
    """
    
    def __init__(self):
        """Initialize MonkeyAPI."""
        super().__init__("monkey")
        self.engine = None
        
    def setup(self, device: str) -> None:
        """
        Initialize the Monkey OCR engine.
        
        Args:
            device: Device to run the model on (e.g., 'cuda:0', 'cpu')
        """
        try:
            self.logger.info(f"Initializing Monkey OCR engine on device: {device}")
            
            # Get model configuration
            config_path = self.config.get("monkey", "config_path", fallback="./config/model_configs.yaml")
            
            # Initialize Monkey OCR engine
            self.engine = get_monkey_engine(config_path)
            self.device = device
            
            self.logger.info("Monkey OCR engine initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Monkey OCR engine: {str(e)}")
            raise RuntimeError(f"Monkey OCR engine initialization failed: {str(e)}")
    
    def predict(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute OCR document recognition using Monkey engine.
        
        Args:
            inputs: Decoded request inputs containing document information
            
        Returns:
            Dict containing OCR results with markdown content and images
        """
        file_path = None
        
        try:
            # Validate engine is initialized
            if self.engine is None:
                raise RuntimeError("Monkey OCR engine not initialized")
            
            # Process file and generate output paths
            file_path, output_base, md_url, dir_name, base_url, file_name = self._handle_file_processing(inputs)
            
            # Create output directories
            os.makedirs(output_base, exist_ok=True)
            image_url_prefix = f"{base_url}/static/{dir_name}/images"
            
            self.logger.info(f"Processing document with OCR: {file_path}")
            self.logger.info(f"Output directory: {output_base}")
            
            # Execute OCR processing with Monkey engine
            md_content, image_files = self.engine.parse(
                file_path=file_path,
                output_dir=output_base,
                image_url_prefix=image_url_prefix,
                file_name=file_name
            )
            
            self.logger.info(f"OCR processing completed successfully")
            self.logger.info(f"Generated {len(image_files)} image files")
            
            return {
                'markdown': md_content,
                'images': image_files,
                'file': md_url,
                'trace_id': inputs.get('trace_id', '')
            }
            
        except Exception as e:
            self.logger.error(f"OCR processing failed: {str(e)}")
            return self._create_error_response(
                code=500,
                message="OCR processing failed",
                detail=str(e),
                trace_id=inputs.get('trace_id', '')
            )
            
        finally:
            # Clean up temporary file
            if file_path:
                self._cleanup_temp_file(file_path)
    
    def decode_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Decode and validate OCR processing request.
        
        Args:
            request: Raw request data
            
        Returns:
            Dict containing parsed request data
        """
        try:
            # Use base class validation with OCR-specific checks
            decoded = super().decode_request(request)
            
            # Add OCR-specific validation
            self._validate_ocr_request(decoded)
            
            return decoded
            
        except Exception as e:
            self.logger.error(f"OCR request decoding failed: {str(e)}")
            raise ValueError(f"Invalid OCR processing request: {str(e)}")
    
    def _validate_ocr_request(self, decoded_request: Dict[str, Any]) -> None:
        """
        Validate OCR-specific request parameters.
        
        Args:
            decoded_request: Decoded request data
            
        Raises:
            ValueError: If OCR-specific validation fails
        """
        # Check if file type is supported for OCR processing
        if decoded_request['type'] == 'url_download':
            file_url = decoded_request['file_url']
            supported_extensions = ['.pdf', '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.webp']
            if not any(file_url.lower().endswith(ext) for ext in supported_extensions):
                self.logger.warning(f"File URL may not be supported for OCR: {file_url}")
        
        # Validate OCR-specific options
        options = decoded_request.get('options', {})
        
        # Validate language settings
        if 'language' in options:
            supported_languages = ['auto', 'en', 'ch', 'ja', 'ko', 'ar', 'hi', 'th', 'vi']
            if options['language'] not in supported_languages:
                raise ValueError(f"Unsupported language for OCR. Supported: {supported_languages}")
        
        # Validate OCR confidence threshold
        if 'confidence_threshold' in options:
            threshold = options['confidence_threshold']
            if not isinstance(threshold, (int, float)) or threshold < 0 or threshold > 1:
                raise ValueError("confidence_threshold must be a number between 0 and 1")
        
        # Validate text detection options
        if 'enable_text_detection' in options:
            if not isinstance(options['enable_text_detection'], bool):
                raise ValueError("enable_text_detection option must be a boolean")
        
        # Validate layout preservation options
        if 'preserve_layout' in options:
            if not isinstance(options['preserve_layout'], bool):
                raise ValueError("preserve_layout option must be a boolean")
        
        self.logger.debug("OCR request validation passed")


# Factory function for easy instantiation
def create_monkey_api() -> MonkeyAPI:
    """
    Factory function to create a MonkeyAPI instance.
    
    Returns:
        MonkeyAPI: Configured MonkeyAPI instance
    """
    return MonkeyAPI()
