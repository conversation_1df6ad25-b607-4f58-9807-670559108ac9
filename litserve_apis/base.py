"""
Base LitAPI class for document processing services.

This module provides the base class that all document processing LitAPIs inherit from,
containing shared functionality for request validation, error handling, and response formatting.
"""

import logging
import os
import tempfile
from abc import abstractmethod
from typing import Any, Dict, Optional, Tuple

import litserve as ls
from fastapi import HTTPException, Request, UploadFile

# Import existing common utilities
from server.api.common import (
    ALLOWED_EXTENSIONS,
    MAX_FILE_SIZE,
    ErrorResponse,
    ParseHeader,
    ParseRequest,
    ParseResponse,
    ParseRespPayload,
    create_error_response,
    gen_output_dir_and_urls,
    validate_file_extension,
    validate_file_size,
)
from utils.config import global_config


class BaseDocumentAPI(ls.LitAPI):
    """
    Base class for all document processing LitAPIs.

    This class provides common functionality including:
    - Request validation and parsing
    - File handling (upload and URL download)
    - Error handling and logging
    - Response formatting
    - Output directory generation
    """

    def __init__(self, model_type: str):
        """
        Initialize the base document API.

        Args:
            model_type: The type of model this API handles (e.g., 'mineru', 'dolphin')
        """
        super().__init__()
        self.model_type = model_type
        self.config = global_config
        self.logger = logging.getLogger(f"{__name__}.{model_type}")

    @abstractmethod
    def setup(self, device: str) -> None:
        """
        Initialize the model engine. Must be implemented by subclasses.

        Args:
            device: Device to run the model on (e.g., 'cuda:0', 'cpu')
        """
        pass

    def decode_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Decode and validate incoming request.

        Args:
            request: Raw request data

        Returns:
            Dict containing parsed request data

        Raises:
            ValueError: If request format is invalid
        """
        try:
            # Validate request structure
            self._validate_request_structure(request)

            # Handle file upload request
            if "file" in request:
                return {
                    "type": "file_upload",
                    "file_data": request["file"],
                    "options": request.get("options", {}),
                    "trace_id": request.get("trace_id", ""),
                    "request_obj": request.get("request_obj"),
                }

            # Handle URL download request
            elif "file_url" in request:
                return {
                    "type": "url_download",
                    "file_url": request["file_url"],
                    "options": request.get("options", {}),
                    "trace_id": request.get("trace_id", ""),
                    "request_obj": request.get("request_obj"),
                }

            # Handle ParseRequest format
            elif "header" in request and "payload" in request:
                payload = request["payload"]
                if "file_url" in payload:
                    return {
                        "type": "url_download",
                        "file_url": payload["file_url"],
                        "options": request.get("options", {}),
                        "trace_id": request["header"].get("traceId", ""),
                        "request_obj": request.get("request_obj"),
                    }

            raise ValueError(
                "Request must contain either 'file' or 'file_url' parameter"
            )

        except Exception as e:
            self.logger.error(f"Request decoding failed: {str(e)}")
            raise ValueError(f"Invalid request format: {str(e)}")

    @abstractmethod
    def predict(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute document processing prediction. Must be implemented by subclasses.

        Args:
            inputs: Decoded request inputs

        Returns:
            Dict containing prediction results
        """
        pass

    def encode_response(self, output: Dict[str, Any]) -> ParseResponse:
        """
        Format prediction output into standard response format.

        Args:
            output: Prediction results

        Returns:
            ParseResponse: Formatted response
        """
        # Handle error responses
        if "error" in output:
            return ErrorResponse(
                code=output.get("code", 500),
                message=output.get("message", "Processing failed"),
                detail=output.get("detail"),
                traceId=output.get("trace_id", ""),
            )

        # Format successful response
        return ParseResponse(
            header=ParseHeader(
                code=0, success="success", traceId=output.get("trace_id", "")
            ),
            payload=ParseRespPayload(
                markdown=output.get("markdown", ""),
                images=output.get("images", []),
                file=output.get("file", ""),
            ),
        )

    def _validate_request_structure(self, request: Dict[str, Any]) -> None:
        """
        Validate basic request structure.

        Args:
            request: Request data to validate

        Raises:
            ValueError: If request structure is invalid
        """
        if not isinstance(request, dict):
            raise ValueError("Request must be a dictionary")

        # Check for required fields
        has_file = "file" in request
        has_url = "file_url" in request
        has_payload_url = (
            "header" in request
            and "payload" in request
            and "file_url" in request.get("payload", {})
        )

        if not (has_file or has_url or has_payload_url):
            raise ValueError("Request must contain file data or file URL")

    def _handle_file_processing(
        self, inputs: Dict[str, Any]
    ) -> Tuple[str, str, str, str, str]:
        """
        Handle file processing (upload or download) and generate output paths.

        Args:
            inputs: Decoded request inputs

        Returns:
            Tuple of (file_path, output_base, md_url, dir_name, base_url, file_name)

        Raises:
            Exception: If file processing fails
        """
        try:
            # Process file based on input type
            if inputs["type"] == "file_upload":
                file_path = self._handle_file_upload(inputs["file_data"])
            else:  # url_download
                file_path = self._handle_url_download(inputs["file_url"])

            # Generate output paths
            request_obj = inputs.get("request_obj")
            if request_obj:
                output_base, md_url, dir_name, base_url, file_name = (
                    gen_output_dir_and_urls(request_obj)
                )
            else:
                # Fallback path generation when no request object available
                output_base, md_url, dir_name, base_url, file_name = (
                    self._generate_fallback_paths()
                )

            return file_path, output_base, md_url, dir_name, base_url, file_name

        except Exception as e:
            self.logger.error(f"File processing failed: {str(e)}")
            raise

    def _handle_file_upload(self, file_data: Any) -> str:
        """
        Handle file upload and save to temporary location.

        Args:
            file_data: Uploaded file data

        Returns:
            str: Path to temporary file
        """
        # If file_data is already a path (from testing), return it
        if isinstance(file_data, str) and os.path.exists(file_data):
            return file_data

        # Handle UploadFile object
        if hasattr(file_data, "filename"):
            # This would be called in async context normally
            # For now, we'll handle it synchronously
            return self._save_upload_file_sync(file_data)

        # Handle raw file content
        if isinstance(file_data, (bytes, bytearray)):
            return self._save_raw_file_content(file_data)

        raise ValueError("Unsupported file data format")

    def _handle_url_download(self, file_url: str) -> str:
        """
        Handle file download from URL.

        Args:
            file_url: URL to download file from

        Returns:
            str: Path to downloaded file
        """
        # This would normally be async, but we'll handle it synchronously for now
        return self._download_file_sync(file_url)

    def _save_upload_file_sync(self, upload_file: UploadFile) -> str:
        """
        Synchronously save uploaded file to temporary location.

        Args:
            upload_file: FastAPI UploadFile object

        Returns:
            str: Path to temporary file
        """
        # Validate file
        if not upload_file.filename:
            raise ValueError("File name cannot be empty")

        if not validate_file_extension(upload_file.filename):
            raise ValueError(
                f"Unsupported file format. Supported: {', '.join(ALLOWED_EXTENSIONS)}"
            )

        # Read file content
        content = upload_file.file.read()
        if not validate_file_size(len(content)):
            raise ValueError(
                f"File size exceeds limit. Maximum: {MAX_FILE_SIZE // (1024*1024)}MB"
            )

        # Save to temporary file
        suffix = os.path.splitext(upload_file.filename)[-1]
        fd, tmp_path = tempfile.mkstemp(suffix=suffix)

        try:
            with os.fdopen(fd, "wb") as tmp_file:
                tmp_file.write(content)

            self.logger.info(f"File saved to temporary location: {tmp_path}")
            return tmp_path

        except Exception as e:
            # Cleanup on error
            try:
                os.unlink(tmp_path)
            except OSError:
                pass
            raise e

    def _save_raw_file_content(self, content: bytes) -> str:
        """
        Save raw file content to temporary location.

        Args:
            content: Raw file content

        Returns:
            str: Path to temporary file
        """
        if not validate_file_size(len(content)):
            raise ValueError(
                f"File size exceeds limit. Maximum: {MAX_FILE_SIZE // (1024*1024)}MB"
            )

        fd, tmp_path = tempfile.mkstemp()
        try:
            with os.fdopen(fd, "wb") as tmp_file:
                tmp_file.write(content)

            self.logger.info(f"Raw content saved to temporary location: {tmp_path}")
            return tmp_path

        except Exception as e:
            try:
                os.unlink(tmp_path)
            except OSError:
                pass
            raise e

    def _download_file_sync(self, file_url: str) -> str:
        """
        Synchronously download file from URL.

        Args:
            file_url: URL to download from

        Returns:
            str: Path to downloaded file
        """
        import httpx

        try:
            with httpx.Client(timeout=30.0) as client:
                resp = client.get(file_url, follow_redirects=True)
                resp.raise_for_status()

                # Validate file size
                content_length = resp.headers.get("content-length")
                if content_length and not validate_file_size(int(content_length)):
                    raise ValueError(
                        f"File size exceeds limit. Maximum: {MAX_FILE_SIZE // (1024*1024)}MB"
                    )

                # Generate filename
                filename = file_url.split("/")[-1] or "file"
                import re

                filename = re.sub(r'[<>:"/\\|?*]', "_", filename)

                if not validate_file_extension(filename):
                    raise ValueError(
                        f"Unsupported file format. Supported: {', '.join(ALLOWED_EXTENSIONS)}"
                    )

                suffix = os.path.splitext(filename)[-1]
                fd, tmp_path = tempfile.mkstemp(suffix=suffix)

                try:
                    with os.fdopen(fd, "wb") as tmp_file:
                        tmp_file.write(resp.content)

                    self.logger.info(
                        f"File downloaded to temporary location: {tmp_path}"
                    )
                    return tmp_path

                except Exception as e:
                    try:
                        os.unlink(tmp_path)
                    except OSError:
                        pass
                    raise e

        except Exception as e:
            self.logger.error(f"Failed to download file from {file_url}: {str(e)}")
            raise ValueError(f"File download failed: {str(e)}")

    def _generate_fallback_paths(self) -> Tuple[str, str, str, str, str]:
        """
        Generate fallback output paths when no request object is available.

        Returns:
            Tuple of (output_base, md_url, dir_name, base_url, file_name)
        """
        import datetime
        import uuid

        now_str = datetime.datetime.now().strftime("%Y%m%d%H%M%S%f")[:-3]
        uuid_str = uuid.uuid4().hex[:8]
        dir_name = f"{now_str}_{uuid_str}"

        output_base = os.path.join("static", dir_name)
        base_url = "http://localhost:11288"  # Default base URL
        md_url = f"{base_url}/static/{dir_name}/{uuid_str}.md"

        return output_base, md_url, dir_name, base_url, uuid_str

    def _cleanup_temp_file(self, file_path: str) -> None:
        """
        Clean up temporary file.

        Args:
            file_path: Path to temporary file to clean up
        """
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
                self.logger.debug(f"Cleaned up temporary file: {file_path}")
        except OSError as e:
            self.logger.warning(
                f"Failed to cleanup temporary file {file_path}: {str(e)}"
            )

    def _create_error_response(
        self,
        code: int,
        message: str,
        detail: Optional[str] = None,
        trace_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Create standardized error response.

        Args:
            code: Error code
            message: Error message
            detail: Detailed error information
            trace_id: Trace ID for error tracking

        Returns:
            Dict containing error response data
        """
        return {
            "error": True,
            "code": code,
            "message": message,
            "detail": detail,
            "trace_id": trace_id or "",
        }
