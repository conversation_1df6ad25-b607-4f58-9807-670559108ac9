"""
DolphinAPI - LitAPI implementation for image document understanding using Dolphin engine.

This module provides a LitAPI implementation specifically for processing image documents
using the Dolphin engine with advanced layout parsing and batch processing capabilities.
"""

import logging
import os
from typing import Any, Dict, List

from .base import BaseDocumentAPI
from engine.dolphin import get_dolphin_engine
from utils.config import global_config


class DolphinAPI(BaseDocumentAPI):
    """
    LitAPI implementation for image document understanding using Dolphin engine.
    
    This API specializes in processing image documents with advanced layout parsing,
    supporting batch processing for improved throughput on multiple images.
    """
    
    def __init__(self):
        """Initialize DolphinAPI."""
        super().__init__("dolphin")
        self.engine = None
        self.max_batch_size = 8
        
    def setup(self, device: str) -> None:
        """
        Initialize the Dolphin engine.
        
        Args:
            device: Device to run the model on (e.g., 'cuda:0', 'cpu')
        """
        try:
            self.logger.info(f"Initializing Dolphin engine on device: {device}")
            
            # Get model configuration
            model_path = self.config.get("dolphin", "model_path", fallback="./resource/dolphin_model")
            self.max_batch_size = self.config.getint("dolphin", "max_batch_size", fallback=8)
            
            # Initialize Dolphin engine
            self.engine = get_dolphin_engine(model_path, device)
            self.device = device
            
            self.logger.info(f"Dolphin engine initialized successfully with max_batch_size={self.max_batch_size}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Dolphin engine: {str(e)}")
            raise RuntimeError(f"Dolphin engine initialization failed: {str(e)}")
    
    def predict(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute image document understanding using Dolphin engine.
        
        Args:
            inputs: Decoded request inputs containing image information
            
        Returns:
            Dict containing parsing results with markdown content
        """
        file_path = None
        
        try:
            # Validate engine is initialized
            if self.engine is None:
                raise RuntimeError("Dolphin engine not initialized")
            
            # Process file and generate output paths
            file_path, output_base, md_url, dir_name, base_url, file_name = self._handle_file_processing(inputs)
            
            # Create output directories
            os.makedirs(output_base, exist_ok=True)
            
            self.logger.info(f"Processing image document: {file_path}")
            self.logger.info(f"Output directory: {output_base}")
            
            # Execute image document processing with Dolphin engine
            md_content = self.engine.process_page(
                image_path=file_path,
                save_dir=output_base,
                base_file_name=file_name,
                max_batch_size=self.max_batch_size
            )
            
            # Collect generated image files
            image_files = self._collect_generated_images(output_base, base_url, dir_name)
            
            self.logger.info(f"Image document processing completed successfully")
            self.logger.info(f"Generated {len(image_files)} image files")
            
            return {
                'markdown': md_content,
                'images': image_files,
                'file': md_url,
                'trace_id': inputs.get('trace_id', '')
            }
            
        except Exception as e:
            self.logger.error(f"Image document processing failed: {str(e)}")
            return self._create_error_response(
                code=500,
                message="Image document processing failed",
                detail=str(e),
                trace_id=inputs.get('trace_id', '')
            )
            
        finally:
            # Clean up temporary file
            if file_path:
                self._cleanup_temp_file(file_path)
    
    def batch(self, inputs_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Batch multiple image processing requests for improved throughput.
        
        Args:
            inputs_list: List of decoded request inputs
            
        Returns:
            List of batched inputs ready for processing
        """
        try:
            self.logger.info(f"Batching {len(inputs_list)} image processing requests")
            
            # Group inputs by processing type for optimal batching
            file_uploads = []
            url_downloads = []
            
            for inputs in inputs_list:
                if inputs['type'] == 'file_upload':
                    file_uploads.append(inputs)
                else:
                    url_downloads.append(inputs)
            
            # Create batched groups
            batched_inputs = []
            
            # Process file uploads in batches
            if file_uploads:
                for i in range(0, len(file_uploads), self.max_batch_size):
                    batch = file_uploads[i:i + self.max_batch_size]
                    batched_inputs.append({
                        'type': 'batch_file_upload',
                        'batch_data': batch,
                        'batch_size': len(batch)
                    })
            
            # Process URL downloads in batches
            if url_downloads:
                for i in range(0, len(url_downloads), self.max_batch_size):
                    batch = url_downloads[i:i + self.max_batch_size]
                    batched_inputs.append({
                        'type': 'batch_url_download',
                        'batch_data': batch,
                        'batch_size': len(batch)
                    })
            
            self.logger.info(f"Created {len(batched_inputs)} batches for processing")
            return batched_inputs
            
        except Exception as e:
            self.logger.error(f"Batching failed: {str(e)}")
            # Fallback to individual processing
            return inputs_list
    
    def unbatch(self, outputs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Unbatch processing results back to individual responses.
        
        Args:
            outputs: List of batched processing results
            
        Returns:
            List of individual response dictionaries
        """
        try:
            unbatched_results = []
            
            for output in outputs:
                if output.get('type') == 'batch_result':
                    # Extract individual results from batch
                    batch_results = output.get('batch_results', [])
                    unbatched_results.extend(batch_results)
                else:
                    # Single result
                    unbatched_results.append(output)
            
            self.logger.info(f"Unbatched {len(unbatched_results)} individual results")
            return unbatched_results
            
        except Exception as e:
            self.logger.error(f"Unbatching failed: {str(e)}")
            # Fallback to original outputs
            return outputs
    
    def decode_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Decode and validate image document processing request.
        
        Args:
            request: Raw request data
            
        Returns:
            Dict containing parsed request data
        """
        try:
            # Use base class validation with image-specific checks
            decoded = super().decode_request(request)
            
            # Add image-specific validation
            self._validate_image_request(decoded)
            
            return decoded
            
        except Exception as e:
            self.logger.error(f"Image request decoding failed: {str(e)}")
            raise ValueError(f"Invalid image processing request: {str(e)}")
    
    def _validate_image_request(self, decoded_request: Dict[str, Any]) -> None:
        """
        Validate image-specific request parameters.
        
        Args:
            decoded_request: Decoded request data
            
        Raises:
            ValueError: If image-specific validation fails
        """
        # Check if file type is supported for image processing
        if decoded_request['type'] == 'url_download':
            file_url = decoded_request['file_url']
            image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.webp']
            if not any(file_url.lower().endswith(ext) for ext in image_extensions):
                self.logger.warning(f"File URL may not be an image: {file_url}")
        
        # Validate processing options
        options = decoded_request.get('options', {})
        
        # Validate batch size if provided
        if 'batch_size' in options:
            batch_size = options['batch_size']
            if not isinstance(batch_size, int) or batch_size < 1 or batch_size > self.max_batch_size:
                raise ValueError(f"Invalid batch_size. Must be between 1 and {self.max_batch_size}")
        
        # Validate layout analysis options
        if 'enable_layout_analysis' in options:
            if not isinstance(options['enable_layout_analysis'], bool):
                raise ValueError("enable_layout_analysis option must be a boolean")
        
        self.logger.debug("Image request validation passed")
    
    def _collect_generated_images(self, output_base: str, base_url: str, dir_name: str) -> List[str]:
        """
        Collect URLs of generated image files.
        
        Args:
            output_base: Base output directory
            base_url: Base URL for static files
            dir_name: Directory name for this processing session
            
        Returns:
            List of image file URLs
        """
        image_files = []
        
        try:
            # Check for images in the output directory
            if os.path.exists(output_base):
                for root, dirs, files in os.walk(output_base):
                    for file in files:
                        if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
                            # Generate relative path from output_base
                            rel_path = os.path.relpath(os.path.join(root, file), output_base)
                            image_url = f"{base_url}/static/{dir_name}/{rel_path}"
                            image_files.append(image_url)
            
            self.logger.debug(f"Collected {len(image_files)} generated image files")
            
        except Exception as e:
            self.logger.warning(f"Failed to collect generated images: {str(e)}")
        
        return image_files


# Factory function for easy instantiation
def create_dolphin_api() -> DolphinAPI:
    """
    Factory function to create a DolphinAPI instance.
    
    Returns:
        DolphinAPI: Configured DolphinAPI instance
    """
    return DolphinAPI()
