# Contributing to Document Converter Service

Thank you for your interest in contributing to the Document Converter Service! This document provides guidelines and information for contributors.

## 🤝 How to Contribute

### Reporting Issues

1. **Search existing issues** first to avoid duplicates
2. **Use the issue template** when creating new issues
3. **Provide detailed information** including:
   - Steps to reproduce the problem
   - Expected vs actual behavior
   - Environment details (OS, Python version, etc.)
   - Log files or error messages

### Suggesting Features

1. **Check the roadmap** to see if the feature is already planned
2. **Open a feature request** with detailed description
3. **Explain the use case** and why it would be valuable
4. **Consider implementation complexity** and maintenance burden

### Code Contributions

#### Prerequisites

- Python 3.12+
- Git
- Basic understanding of FastAPI and async programming
- Familiarity with AI/ML model integration

#### Development Setup

1. **Fork the repository**
   ```bash
   git clone https://github.com/your-username/doc-convert.git
   cd doc-convert
   ```

2. **Create a virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   pip install -r requirements-dev.txt  # If available
   ```

4. **Set up environment variables**
   ```bash
   export CONFIG_FILE_PATH=$(pwd)/config/config.ini
   export MINERU_TOOLS_CONFIG_JSON=$(pwd)/config/magic-pdf.json
   ```

5. **Run tests to ensure everything works**
   ```bash
   python -m pytest test/
   ```

#### Making Changes

1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Follow coding standards**
   - Use type hints for all function parameters and return values
   - Add docstrings to all public functions and classes
   - Follow PEP 8 style guidelines
   - Use meaningful variable and function names

3. **Write tests**
   - Add unit tests for new functionality
   - Ensure existing tests still pass
   - Aim for good test coverage

4. **Update documentation**
   - Update README.md if needed
   - Add API documentation for new endpoints
   - Update configuration documentation

5. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

#### Commit Message Guidelines

Use conventional commit format:
- `feat:` for new features
- `fix:` for bug fixes
- `docs:` for documentation changes
- `style:` for formatting changes
- `refactor:` for code refactoring
- `test:` for adding tests
- `chore:` for maintenance tasks

#### Pull Request Process

1. **Update your branch**
   ```bash
   git fetch origin
   git rebase origin/main
   ```

2. **Push your changes**
   ```bash
   git push origin feature/your-feature-name
   ```

3. **Create a Pull Request**
   - Use a clear, descriptive title
   - Fill out the PR template completely
   - Link related issues
   - Request review from maintainers

4. **Address feedback**
   - Respond to review comments
   - Make requested changes
   - Update tests and documentation as needed

## 📋 Code Standards

### Python Code Style

- Follow PEP 8
- Use Black for code formatting
- Use isort for import sorting
- Maximum line length: 88 characters

### Documentation

- All public functions must have docstrings
- Use Google-style docstrings
- Include type hints for all parameters
- Document complex algorithms and business logic

### Testing

- Write unit tests for all new functionality
- Use pytest for testing framework
- Mock external dependencies
- Test both success and error cases

### API Design

- Follow RESTful principles
- Use consistent response formats
- Include proper HTTP status codes
- Document all endpoints with OpenAPI

## 🔍 Code Review Guidelines

### For Contributors

- Keep PRs focused and small
- Write clear commit messages
- Include tests and documentation
- Be responsive to feedback

### For Reviewers

- Be constructive and helpful
- Focus on code quality and maintainability
- Check for security issues
- Verify tests and documentation

## 🚀 Release Process

1. **Version Bumping**: Follow semantic versioning (MAJOR.MINOR.PATCH)
2. **Changelog**: Update CHANGELOG.md with new features and fixes
3. **Testing**: Ensure all tests pass and manual testing is complete
4. **Documentation**: Update version-specific documentation
5. **Tagging**: Create git tags for releases

## 📞 Getting Help

- **Discord/Slack**: Join our community chat
- **GitHub Discussions**: For general questions and discussions
- **GitHub Issues**: For bug reports and feature requests
- **Email**: Contact maintainers directly for sensitive issues

## 🏆 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- Project documentation

Thank you for contributing to making document processing more accessible and efficient!
