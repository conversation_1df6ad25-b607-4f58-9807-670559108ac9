# Use official Python 3.12 slim image as base
FROM artifacts.iflytek.com/docker-private/datahub/lynxiao/python:3.12-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV HF_ENDPOINT=https://hf-mirror.com
# ENV MINERU_TOOLS_CONFIG_JSON=/app/magic-pdf.json

# 修改镜像源（适配旧新Debian）
RUN [ -f /etc/apt/sources.list ] && sed -i '<EMAIL>@mirrors.tuna.tsinghua.edu.cn@g' /etc/apt/sources.list || true
RUN [ -f /etc/apt/sources.list.d/debian.sources ] && sed -i '<EMAIL>@mirrors.tuna.tsinghua.edu.cn@g' /etc/apt/sources.list.d/debian.sources || true

WORKDIR /app

# Install system dependencies (add or remove as per your project needs)
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    gcc \
    build-essential \
    libpoppler-cpp-dev \
    pkg-config \
    python3-dev \
    libreoffice \
    libgl1-mesa-glx \
    && rm -rf /var/lib/apt/lists/*

# Copy project files
COPY . .

# Install Python dependencies
RUN pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple \
    && pip install --no-cache-dir -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple

# downoad model
# RUN python ./script/download_models_hf.py
# RUN huggingface-cli download ByteDance/Dolphin --local-dir ./hf_model

# Expose port
# EXPOSE 8000

# Start FastAPI with Uvicorn
# CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]

# Start FastAPI with Gunicorn
# CMD ["gunicorn", "main:app", "-k", "uvicorn.workers.UvicornWorker", "--workers", "4", "--bind", "0.0.0.0:8000", "--timeout", "30", "--max-requests", "1000", "--max-requests-jitter", "100"]