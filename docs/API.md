# Document Converter API Documentation

This document provides detailed information about the Document Converter Service API endpoints.

## Base URL

```
http://localhost:11288
```

## Authentication

Currently, the API does not require authentication. This may change in future versions.

## Common Response Format

All API endpoints return responses in the following format:

```json
{
  "header": {
    "code": 0,
    "success": "success",
    "traceId": "optional-trace-id",
    "skynet_tlb_service_tag_selector": ""
  },
  "payload": {
    "markdown": "# Document Content\n\nParsed content...",
    "images": ["http://localhost:11288/static/.../image1.png"],
    "file": "http://localhost:11288/static/.../document.md"
  }
}
```

## Error Response Format

```json
{
  "code": 400,
  "message": "Error description",
  "detail": "Detailed error information",
  "traceId": "optional-trace-id"
}
```

## System Endpoints

### GET /

Returns basic service information.

**Response:**
```json
{
  "service": "Document Converter Service",
  "version": "1.0.0",
  "description": "A FastAPI-based service for converting documents",
  "docs": "/docs",
  "health": "/health"
}
```

### GET /health

Health check endpoint for monitoring service status.

**Response:**
```json
{
  "status": "healthy",
  "service": "document-converter"
}
```

## Mineru API

Specialized for PDF document processing with advanced OCR capabilities.

### POST /api/mineru/v1/parse_file

Upload and parse a PDF file.

**Request:**
- Method: `POST`
- Content-Type: `multipart/form-data`
- Body: Form data with `file` field containing the PDF file

**Example:**
```bash
curl -X POST "http://localhost:11288/api/mineru/v1/parse_file" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.pdf"
```

**Response:** Standard response format with parsed markdown content and extracted images.

### POST /api/mineru/v1/parse_url

Parse a PDF file from a URL.

**Request:**
- Method: `POST`
- Content-Type: `application/json`

**Body:**
```json
{
  "header": {
    "traceId": "optional-trace-id"
  },
  "payload": {
    "file_url": "https://example.com/document.pdf"
  }
}
```

**Response:** Standard response format with parsed content.

## Dolphin API

Advanced document image understanding and layout parsing.

### POST /api/dolphin/v1/parse_file

Upload and parse a document image.

**Request:**
- Method: `POST`
- Content-Type: `multipart/form-data`
- Body: Form data with `file` field containing the image file

**Supported formats:** PNG, JPG, JPEG, GIF, BMP, TIFF, WEBP

**Example:**
```bash
curl -X POST "http://localhost:11288/api/dolphin/v1/parse_file" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.png"
```

### POST /api/dolphin/v1/parse_url

Parse a document image from a URL.

**Request:** Same format as Mineru v1/parse_url endpoint.

## Monkey API

Specialized OCR solution for document processing with multilingual text recognition.

### POST /api/monkey/v1/parse_file

Upload and parse a document using Monkey OCR.

**Request:**
- Method: `POST`
- Content-Type: `multipart/form-data`
- Body: Form data with `file` field containing the document file

**Supported formats:** PDF, PNG, JPG, JPEG, GIF, BMP, TIFF, WEBP

**Example:**
```bash
curl -X POST "http://localhost:11288/api/monkey/v1/parse_file" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.pdf"
```

### POST /api/monkey/v1/parse_url

Parse a document from a URL using Monkey OCR.

**Request:** Same format as other v1/parse_url endpoints.

## MarkItDown API

General-purpose document converter for various file formats.

### POST /api/markitdown/v1/parse_file

Upload and parse various document formats.

**Supported formats:** PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX, and image formats

**Request:**
- Method: `POST`
- Content-Type: `multipart/form-data`
- Body: Form data with `file` field

### POST /api/markitdown/v1/parse_url

Parse a document from a URL.

**Request:** Same format as other v1/parse_url endpoints.

## File Size and Format Limitations

- **Maximum file size:** 100MB
- **Supported formats:**
  - **Documents:** PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX
  - **Images:** PNG, JPG, JPEG, GIF, BMP, TIFF, WEBP

## Error Codes

| Code | Description |
|------|-------------|
| 0    | Success |
| 400  | Bad Request (invalid input) |
| 413  | File too large |
| 500  | Internal server error |
| 510  | File upload/download failed |
| 511  | Document parsing failed |

## Rate Limiting

Currently, no rate limiting is implemented. This may be added in future versions.

## Examples

### Python Example

```python
import requests

# Upload file
with open('document.pdf', 'rb') as f:
    response = requests.post(
        'http://localhost:11288/api/mineru/v1/parse_file',
        files={'file': f}
    )
    result = response.json()
    print(result['payload']['markdown'])

# Parse from URL
response = requests.post(
    'http://localhost:11288/api/dolphin/v1/parse_url',
    json={
        'header': {'traceId': 'example-123'},
        'payload': {'file_url': 'https://example.com/image.png'}
    }
)
result = response.json()
```

### JavaScript Example

```javascript
// Upload file
const formData = new FormData();
formData.append('file', fileInput.files[0]);

fetch('http://localhost:11288/api/markitdown/v1/parse_file', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(data => console.log(data.payload.markdown));

// Parse from URL
fetch('http://localhost:11288/api/mineru/v1/parse_url', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        header: {traceId: 'js-example'},
        payload: {file_url: 'https://example.com/document.pdf'}
    })
})
.then(response => response.json())
.then(data => console.log(data));
```

## Interactive Documentation

For interactive API testing, visit:
- **Swagger UI:** http://localhost:11288/docs
- **ReDoc:** http://localhost:11288/redoc

These interfaces provide detailed schema information and allow you to test endpoints directly from your browser.
