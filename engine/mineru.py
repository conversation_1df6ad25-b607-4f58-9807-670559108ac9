import os
from pathlib import Path

from magic_pdf.config.enums import SupportedPdfParseMethod
from magic_pdf.data.data_reader_writer import FileBasedDataReader, FileBasedDataWriter
from magic_pdf.data.dataset import PymuDocDataset
from magic_pdf.model.doc_analyze_by_custom_model import ModelSingleton, doc_analyze

# 设置环境变量 - 使用相对路径
if "MINERU_TOOLS_CONFIG_JSON" not in os.environ:
    project_root = Path(__file__).parent.parent
    config_path = project_root / "config" / "magic-pdf.json"
    os.environ["MINERU_TOOLS_CONFIG_JSON"] = str(config_path)


class MineruEngine:
    def __init__(self):
        model = ModelSingleton()
        model.get_model(
            ocr=True,
            show_log=False,
            lang=None,
            layout_model=None,
            formula_enable=None,
            table_enable=None,
        )
        self.model = model

    def parse(self, file_path: str, output_dir: str, image_url_prefix: str):
        """
        解析PDF文件，返回markdown内容和图片文件列表

        Args:
            file_path (str): PDF文件路径
            output_dir (str): 输出目录
            image_url_prefix (str): 图片URL前缀

        Returns:
            tuple: (markdown内容, 图片文件URL列表)
        """
        os.makedirs(output_dir, exist_ok=True)
        local_image_dir = os.path.join(output_dir, "images")
        os.makedirs(local_image_dir, exist_ok=True)

        reader = FileBasedDataReader("")
        pdf_bytes = reader.read(file_path)
        ds = PymuDocDataset(pdf_bytes)
        if ds.classify() == SupportedPdfParseMethod.OCR:
            infer_result = ds.apply(doc_analyze, ocr=True, show_log=False)
            pipe_result = infer_result.pipe_ocr_mode(
                FileBasedDataWriter(local_image_dir)
            )
        else:
            infer_result = ds.apply(doc_analyze, ocr=False, show_log=False)
            pipe_result = infer_result.pipe_txt_mode(
                FileBasedDataWriter(local_image_dir)
            )

        file_name = output_dir.split("_")[1] if "_" in output_dir else "output"
        md_content = pipe_result.get_markdown(image_url_prefix)
        pipe_result.dump_md(
            FileBasedDataWriter(output_dir), f"{file_name}.md", image_url_prefix
        )

        image_files = [
            f"{image_url_prefix}/{fname}"
            for fname in os.listdir(local_image_dir)
            if fname.lower().endswith((".png", ".jpg", ".jpeg", ".gif", ".bmp"))
        ]
        return md_content, image_files


def get_mineru_engine():
    """
    获取MineruEngine实例

    Returns:
        MineruEngine: 实例化的MineruEngine对象
    """
    return MineruEngine()
