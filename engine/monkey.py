import os
import time

from engine.magic_pdf_v2.data.data_reader_writer import (
    FileBasedDataReader,
    FileBasedDataWriter,
)
from engine.magic_pdf_v2.data.dataset import ImageDataset, PymuDocDataset
from engine.magic_pdf_v2.model.custom_model import MonkeyOCR
from engine.magic_pdf_v2.model.doc_analyze_by_custom_model_llm import doc_analyze_llm


class MonkeyEngine:
    def __init__(self, config_path: str = None):
        self.model = MonkeyOCR(config_path)

    def parse(
        self,
        file_path: str,
        output_dir: str,
        image_url_prefix: str,
        file_name: str = "output",
    ) -> tuple[str, list[str]]:
        """
        解析文件，返回markdown内容和图片文件列表

        Args:
            file_path (str): 文件路径
            output_dir (str): 输出目录
            image_url_prefix (str): 图片URL前缀
            file_name (str): 输出文件名（不带扩展名）

        Returns:
            tuple: (markdown内容, 图片文件URL列表)
        """
        os.makedirs(output_dir, exist_ok=True)
        local_image_dir = os.path.join(output_dir, "images")
        os.makedirs(local_image_dir, exist_ok=True)

        reader = FileBasedDataReader()
        pdf_bytes = reader.read(file_path)
        # 判断文件类型
        if file_path.lower().endswith(".pdf"):
            ds = PymuDocDataset(pdf_bytes)
        else:
            ds = ImageDataset(pdf_bytes)

        t1 = time.time()
        infer_result = ds.apply(doc_analyze_llm, MonkeyOCR_model=self.model)
        pipe_result = infer_result.pipe_ocr_mode(
            FileBasedDataWriter(local_image_dir), MonkeyOCR_model=self.model
        )

        # 可选：保存可视化结果
        infer_result.draw_model(os.path.join(output_dir, f"{file_name}_model.pdf"))
        pipe_result.draw_layout(os.path.join(output_dir, f"{file_name}_layout.pdf"))
        pipe_result.draw_span(os.path.join(output_dir, f"{file_name}_spans.pdf"))

        md_content = pipe_result.get_markdown(image_url_prefix)
        pipe_result.dump_md(
            FileBasedDataWriter(output_dir), f"{file_name}.md", image_url_prefix
        )

        image_files = [
            f"{image_url_prefix}/{fname}"
            for fname in os.listdir(local_image_dir)
            if fname.lower().endswith((".png", ".jpg", ".jpeg", ".gif", ".bmp"))
        ]
        return md_content, image_files


def get_monkey_engine(config_path: str = None) -> MonkeyEngine:
    """
    Get the Monkey OCR engine instance.

    Args:
        model_path (str): Path to the model file.

    Returns:
        MonkeyEngine: An instance of the MonkeyEngine class.
    """
    return MonkeyEngine(config_path=config_path)
