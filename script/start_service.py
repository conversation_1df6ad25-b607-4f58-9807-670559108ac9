#!/usr/bin/env python3
"""
Enhanced service startup script with better error handling and logging.
"""

import argparse
import logging
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def setup_logging():
    """Setup logging for the startup script."""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )
    return logging.getLogger(__name__)


def check_environment():
    """Check if the environment is properly configured."""
    logger = logging.getLogger(__name__)

    # Check environment variables
    required_env_vars = ["CONFIG_FILE_PATH", "MINERU_TOOLS_CONFIG_JSON"]

    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
        else:
            logger.info(f"✅ {var} = {os.getenv(var)}")

    if missing_vars:
        logger.error(f"❌ Missing environment variables: {missing_vars}")
        logger.info("Please set the following environment variables:")
        logger.info("export CONFIG_FILE_PATH=/path/to/doc-convert/config/config.ini")
        logger.info(
            "export MINERU_TOOLS_CONFIG_JSON=/path/to/doc-convert/config/magic-pdf.json"
        )
        return False

    # Check config files exist
    config_file = os.getenv("CONFIG_FILE_PATH")
    mineru_config = os.getenv("MINERU_TOOLS_CONFIG_JSON")

    if not os.path.exists(config_file):
        logger.error(f"❌ Config file not found: {config_file}")
        return False

    if not os.path.exists(mineru_config):
        logger.error(f"❌ Mineru config file not found: {mineru_config}")
        return False

    logger.info("✅ Environment configuration is valid")
    return True


def check_dependencies():
    """Check if required dependencies are available."""
    logger = logging.getLogger(__name__)

    required_modules = [
        "fastapi",
        "uvicorn",
        "magic_pdf",
        "markitdown",
        "transformers",
        "torch",
    ]

    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            logger.info(f"✅ {module}")
        except ImportError:
            missing_modules.append(module)
            logger.error(f"❌ {module}")

    if missing_modules:
        logger.error(f"Missing required modules: {missing_modules}")
        logger.info("Please install missing dependencies:")
        logger.info("pip install -r requirements.txt")
        return False

    logger.info("✅ All required dependencies are available")
    return True


def start_service(host="0.0.0.0", port=8000, workers=1, reload=False):
    """Start the document converter service."""
    logger = logging.getLogger(__name__)

    try:
        # Import and create the app
        from main import create_app

        app = create_app()

        logger.info(f"Starting service on {host}:{port}")
        logger.info(f"Workers: {workers}")
        logger.info(f"Reload: {reload}")

        # Start with uvicorn
        import uvicorn

        uvicorn.run(
            "main:app",
            host=host,
            port=port,
            workers=(
                workers if not reload else 1
            ),  # reload doesn't work with multiple workers
            reload=reload,
            log_level="info",
        )

    except Exception as e:
        logger.error(f"Failed to start service: {e}")
        import traceback

        traceback.print_exc()
        return False

    return True


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Start the Document Converter Service")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument(
        "--workers", type=int, default=1, help="Number of worker processes"
    )
    parser.add_argument(
        "--reload", action="store_true", help="Enable auto-reload for development"
    )
    parser.add_argument(
        "--check-only",
        action="store_true",
        help="Only check environment and dependencies",
    )

    args = parser.parse_args()

    logger = setup_logging()
    logger.info("🚀 Document Converter Service Startup")
    logger.info("=" * 50)

    # Check environment
    if not check_environment():
        logger.error("Environment check failed")
        sys.exit(1)

    # Check dependencies
    if not check_dependencies():
        logger.error("Dependency check failed")
        sys.exit(1)

    if args.check_only:
        logger.info("✅ All checks passed. Service is ready to start.")
        sys.exit(0)

    # Start the service
    logger.info("Starting the service...")
    success = start_service(
        host=args.host, port=args.port, workers=args.workers, reload=args.reload
    )

    if not success:
        logger.error("Failed to start service")
        sys.exit(1)


if __name__ == "__main__":
    main()
