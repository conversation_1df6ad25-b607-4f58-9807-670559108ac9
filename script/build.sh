#!/bin/bash

VERSION=v0.1.5

# Build the Docker image
docker build -t doc-convert:$VERSION -f ../cicd/Dockerfile ..

# Run the Docker container with the specified uvicorn command
# docker run -d -p 18115:8000 -v /opt/jfs/fengli16/static:/app/static --gpus all  --name doc-convert-$VERSION doc-convert:$VERSION uvicorn main:app --host 0.0.0.0 --port 8000 --workers 1

docker save -o doc-convert-$VERSION.tar doc-convert:$VERSION
