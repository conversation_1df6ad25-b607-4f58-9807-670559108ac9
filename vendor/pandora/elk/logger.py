import logging
from logging.handlers import RotatingFileHandler
from .proto import KafkaProto
import json
from dataclasses import asdict
import os

class ElkLogger:
    def __init__(self):
        self.initial = False
    
    def init_log(self,name: str="default", log_path: str = "app.json", level: str = "INFO", rotate_size: int = 100 * 1024 * 1024, backup_count: int = 30):
        """初始化日志器
        - log_path: 日志文件路径
        - level: 日志等级（DEBUG, INFO, WARNING, ERROR, CRITICAL）
        - rotate_size: 日志轮转大小（单位：字节）
        - backup_count: 最大保留的日志文件个数
        """

        if os.path.dirname(log_path) != "":
            os.makedirs(os.path.dirname(log_path), exist_ok=True)
        self.type = name
        self.logger = logging.getLogger("elK_logger")
        self.logger.setLevel(getattr(logging, level.upper(), logging.INFO))

        formatter = logging.Formatter('%(message)s')

        # 支持日志轮转
        handler = RotatingFileHandler(log_path, maxBytes=rotate_size, backupCount=backup_count)
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.initial = True

        # 控制台也输出
        #console = logging.StreamHandler()
        #console.setFormatter(formatter)
        #self.logger.addHandler(console)
        # 
    def info(self, kp: KafkaProto):
        """记录 info 级别日志"""
        self._log("info",self.logger.info, kp)

    def debug(self, kp: KafkaProto):
        """记录 debug 级别日志"""
        self._log("debug",self.logger.debug, kp)

    def error(self, kp: KafkaProto):
        """记录 error 级别日志"""
        self._log("error",self.logger.error, kp)

    def _log(self, level,log_func, kp: KafkaProto):
        kp.fill(level=level,name=self.type)
        try:
            json_msg = json.dumps(asdict(kp), ensure_ascii=False)
            log_func(json_msg)
        except Exception as e:
            self.logger.exception(f"序列化 KafkaProto 失败: {e}")