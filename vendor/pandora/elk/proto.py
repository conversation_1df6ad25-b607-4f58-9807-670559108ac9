from dataclasses import dataclass, field
from typing import Dict, Any
import time
from .utils import *

@dataclass
class TraceBase:
    spanId: str = ""
    traceId: str = ""
    level: str = ""
    type: str = ""
    tags: Dict[str, Any] = field(default_factory=dict)
    timestamp: int = 0

    action: str = ""
    ip: str = ""
    subjectCode: str = ""

    group: str = ""
    fromId: str = ""
    port: int = 0
    pid: int = 0

    uuid: str = ""
    def fill(self,level = "INFO",name = "default"):
        if self.level == "":
            self.level = level
        if self.ip == "":
            self.ip = host_ip
        if self.timestamp == 0:
            self.timestamp = int(time.time() * 1000)
        if self.pid == 0:
            self.pid = pid
        if self.action == "":
            self.action = name
        

@dataclass
class KafkaProto(TraceBase):
    span: str = ""
    data: str = ""
