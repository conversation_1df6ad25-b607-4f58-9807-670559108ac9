import importlib.util
import inspect
import os

def load_function(file_path: str, function_name: str):
    """从文件中加载指定名字且符合 (name) 签名的函数"""
    module_name = os.path.splitext(os.path.basename(file_path))[0]

    spec = importlib.util.spec_from_file_location(module_name, file_path)
    if spec is None or spec.loader is None:
        raise ImportError(f"无法加载模块：{file_path}")
    
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)

    func = getattr(module, function_name, None)
    if func is None:
        raise AttributeError(f"函数 {function_name} 不存在于文件 {file_path}")

    # 检查函数签名
    '''
    sig = inspect.signature(func)
    params = list(sig.parameters.values())
    if not (len(params) == 1 and params[0].name == "name"):
        raise TypeError(f"函数 {function_name} 签名不符合要求，应为 xxx(name)")
    '''
    return func
