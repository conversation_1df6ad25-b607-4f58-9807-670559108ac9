from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
import time
from ..context.pandora_context import PandoraContext
from ..elk import *

class ContextMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        server_name = get_global_logger().type
        pc = PandoraContext.with_fastapi(request,server_name)
        trace_id = request.headers.get("traceId","")
        header_tag = request.headers.get("skynet-tlb-service-tag-selector","")

        pc.context.set("traceId",trace_id)
        pc.context.set("headerTag",header_tag)
        pc.root_span.set_attribute("traceId",trace_id)
        pc.root_span.set_attribute("headerTag",header_tag)
        request.state.pandora_context = pc
        response = await call_next(request)
        
        pc.root_span.finish()
        duration = pc.root_span.duration()
        get_global_logger().info(proto.KafkaProto(traceId=trace_id,spanId=pc.root_span.to_json(),tags={
            "cost":duration,
        }))
        return response

