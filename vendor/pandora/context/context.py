import threading
import time
import typing

# 定义一个上下文类，用于管理线程的取消事件、时间和键值对数据
class Context:
    def __init__(self):
        # 初始化一个线程事件，用于控制上下文的取消操作
        self._cancel_event = threading.Event()
        # 记录上下文创建的时间
        self._start_time = time.time()
        # 记录上下文的原始创建时间
        self._origin_time = self._start_time
        # 初始化一个线程锁，用于保证对共享数据的线程安全访问
        self._lock = threading.Lock()
        # 用于存储上下文的键值对数据
        self._values = {}
        # 初始化一个定时器，用于处理超时操作
        self._timer = None
        self._state = {}

    @classmethod
    def with_context(cls,parent_ctx: 'Context'):
        """
        创建一个新的上下文对象，并继承父上下文的部分属性。

        :param parent_ctx: 父上下文对象，如果不为 None，则新上下文将继承其取消事件、开始时间和原始时间。
        :return: 新的上下文对象。
        """
        ctx = cls()
        if parent_ctx:
            # 继承父上下文的取消事件
            ctx._cancel_event = parent_ctx._cancel_event
            # 继承父上下文的开始时间
            ctx._start_time = parent_ctx._start_time
            # 继承父上下文的原始时间
            ctx._origin_time = parent_ctx._origin_time
            # 浅拷贝父上下文的键值对数据
            ctx._values = parent_ctx.values_copy()
        return ctx

    @classmethod
    def with_timeout(cls,timeout):
        """
        创建一个带有超时机制的上下文对象。

        :param timeout: 超时时间（秒）。
        :return: 带有超时机制的上下文对象。
        """
        ctx = cls() 
        # 创建一个定时器，超时后调用 cancel 方法取消上下文
        ctx._timer = threading.Timer(timeout, ctx.cancel)
        # 启动定时器
        ctx._timer.start()
        return ctx

    def wait(self):
        """
        阻塞当前线程，直到上下文被取消。
        """
        self._cancel_event.wait()

    def wait_timeout(self,timeout=None):
        """
        阻塞当前线程，直到上下文被取消或超时。

        :param timeout: 超时时间（秒），如果为 None，则无限等待。
        """
        self._cancel_event.wait(timeout)
        
    # 浅拷贝
    def values_copy(self):
        """
        浅拷贝当前上下文的键值对数据。

        :return: 浅拷贝后的键值对数据。
        """
        with self._lock:
            return self._values.copy()

    def cancel(self):
        """
        取消当前上下文，设置取消事件。
        """
        self._cancel_event.set()

    def start_time(self):
        """
        获取上下文的开始时间。

        :return: 上下文的开始时间。
        """
        return self._start_time

    def origin_time(self):
        """
        获取上下文的原始创建时间。

        :return: 上下文的原始创建时间。
        """
        return self._origin_time
    
    def done(self):
        """
        检查上下文是否已被取消。

        :return: 如果上下文已被取消，则返回 True，否则返回 False。
        """
        return self._cancel_event.is_set()

    def set(self, key, value):
        """
        在上下文中设置一个键值对。

        :param key: 键。
        :param value: 值。
        """
        with self._lock:
            self._values[key] = value

    def get(self, key, default=None):
        """
        从上下文中获取一个键对应的值。

        :param key: 键。
        :param default: 如果键不存在，返回的默认值。
        :return: 键对应的值，如果键不存在，则返回默认值。
        """
        with self._lock:
            return self._values.get(key, default)

if __name__ == '__main__':
    ctx = Context()
    ctx.set(1,2)
    ctx2 = Context.with_context(ctx)
    print(ctx2.get(1))

    ctx3 = Context.with_timeout(3)

    ctx4 = Context.with_context(ctx3)
    time.sleep(4)
    print(ctx4.done())