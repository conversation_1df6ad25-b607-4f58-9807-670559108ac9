# 文档转换服务

一个基于 FastAPI 的高性能文档转换服务，支持将多种文档格式（PDF、图片、Office 文档）转换为 Markdown，集成了 Mineru、Dolphin、Monkey OCR 和 MarkItDown 等多种 AI 模型。

## 🚀 功能特性

- **多模型支持**：针对不同文档类型，集成四种强大的 AI 模型
  - **Mineru**：专注于 PDF 文档解析，基于 magic-pdf-v2，具备强大的 OCR 能力
  - **Dolphin**：高级文档图片理解与版面解析，支持复杂布局识别
  - **Monkey OCR**：专业的 OCR 解决方案，支持多语言文本识别
  - **MarkItDown**：通用文档转换器，支持 Office 文件等多种格式

- **多种输入方式**：支持文件上传和基于 URL 的远程文件处理
- **RESTful API**：简洁、文档齐全的 REST API，带有 OpenAPI/Swagger 文档
- **结构化输出**：统一的 JSON 响应格式，包含 markdown 内容和图片链接
- **智能文件管理**：自动静态文件清理，可配置保留策略
- **生产就绪**：完善的日志、错误处理、健康监控和优雅关闭
- **可扩展架构**：模块化设计，支持动态模型加载，关注点分离
- **服务网格支持**：可选的 skynet-mesh sidecar 服务集成

## 📋 环境要求

- **Python**：3.12 及以上
- **CUDA**：用于 GPU 加速（可选，推荐）
- **内存**：建议 8GB 及以上以加载模型
- **存储**：建议 10GB 及以上用于模型文件

## 🛠️ 安装步骤

### 1. 克隆仓库
```bash
git clone <repository-url>
cd doc-convert
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 下载模型文件
```bash
# 下载所需模型
python script/download_models_hf.py
```

### 4. 配置环境变量
```bash
# 主配置文件路径
export CONFIG_FILE_PATH=/path/to/doc-convert/config/config.ini

# Mineru 模型配置
export MINERU_TOOLS_CONFIG_JSON=/path/to/doc-convert/config/magic-pdf.json
```

### 5. 配置模型（可选）
```bash
# 编辑配置文件启用所需模型
# 在 config/config.ini 中设置 models 参数
# 例如：models = mineru,dolphin,monkey,markitdown
```

## ⚙️ 配置说明

服务使用 `config/` 目录下的配置文件：

- `config.ini`：主服务配置（服务器、模型、清理策略等）
- `magic-pdf.json`：Mineru 模型配置
- `model_configs.yaml`：Monkey OCR 模型配置
- `side-car.toml`：Skynet-mesh sidecar 服务配置

### 主要配置项示例

```ini
[version]
version = 1.0.0
title = Document Converter Service
models = mineru,dolphin,monkey,markitdown

[server]
host = 0.0.0.0
port = 11288

[cleanup]
max_age_days = 7
max_files = 1000

[upload]
max_file_size_mb = 100
allowed_extensions = pdf,png,jpg,jpeg,gif,bmp,tiff,webp,doc,docx,ppt,pptx,xls,xlsx

[dolphin]
model_path = ./resource/dolphin_model
device = cuda:0
max_batch_size = 8

[elk]
name = doc-convert
level = INFO
log_path = ./logs/doc_convert.jsonl
```

## 🚀 快速开始

### 启动服务
```bash
# 开发模式（单进程，自动重载）
python main.py

# 使用增强启动脚本（推荐）
python script/start_service.py --host 0.0.0.0 --port 11288 --reload

# 生产模式（Gunicorn 多进程）
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:11288

# 使用 Shell 脚本（包含 sidecar 服务）
./run.sh -p 11288 -w 1

# Docker 部署
bash script/build.sh
```

### API 文档
服务启动后访问：
- **Swagger UI**: http://localhost:11288/docs
- **ReDoc**: http://localhost:11288/redoc
- **健康检查**: http://localhost:11288/health
- **服务信息**: http://localhost:11288/

## 📚 API 使用示例

### 上传文件处理
```bash
# PDF 文档处理（Mineru）
curl -X POST "http://localhost:11288/api/mineru/v1/parse_file" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.pdf"

# 图片文档处理（Dolphin）  
curl -X POST "http://localhost:11288/api/dolphin/v1/parse_file" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.png"

# OCR 文档处理（Monkey）
curl -X POST "http://localhost:11288/api/monkey/v1/parse_file" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.pdf"

# Office 文档处理（MarkItDown）
curl -X POST "http://localhost:11288/api/markitdown/v1/parse_file" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.docx"
```

### 通过 URL 处理文件
```bash
curl -X POST "http://localhost:11288/api/dolphin/v1/parse_url" \
  -H "Content-Type: application/json" \
  -d '{
    "header": {"traceId": "test-123"},
    "payload": {"file_url": "https://example.com/document.pdf"}
  }'
```

### 响应格式
```json
{
  "header": {
    "code": 0,
    "success": "success",
    "traceId": "test-123"
  },
  "payload": {
    "markdown": "# Document Content\n\nParsed markdown content...",
    "images": ["http://localhost:11288/static/.../image1.png"],
    "file": "http://localhost:11288/static/.../document.md"
  }
}
```

## 🏗️ 项目结构

```
doc-convert/
├── main.py                 # 应用入口
├── requirements.txt        # Python 依赖
├── config/                 # 配置文件目录
│   ├── config.ini         # 主服务配置
│   ├── magic-pdf.json     # Mineru 模型配置
│   ├── model_configs.yaml # Monkey OCR 配置
│   └── side-car.toml      # Sidecar 服务配置
├── server/                 # API 服务实现
│   ├── api/               # API 路由处理
│   │   ├── common.py      # 通用工具与模型
│   │   ├── dolphin_api.py # Dolphin 模型接口
│   │   ├── mineru_api.py  # Mineru 模型接口
│   │   ├── monkey_api.py  # Monkey OCR 接口
│   │   └── markitdown_api.py # MarkItDown 接口
│   ├── app_factory.py     # FastAPI 应用工厂
│   └── start.py           # 服务启动逻辑
├── engine/                 # 处理引擎
│   ├── dolphin.py         # Dolphin 引擎
│   ├── mineru.py          # Mineru 引擎
│   ├── monkey.py          # Monkey OCR 引擎
│   └── magic_pdf_v2/      # Magic-PDF v2 完整实现
├── utils/                  # 工具模块
│   ├── config/            # 配置管理
│   ├── dolphin/           # Dolphin 专用工具
│   ├── graceful_stop/     # 优雅停机处理
│   ├── logger/            # 日志配置
│   ├── static_cleanup.py  # 静态文件清理
│   └── temp_file_manager.py # 临时文件管理
├── test/                   # 测试脚本
├── script/                 # 工具脚本
│   ├── build.sh           # Docker 构建脚本
│   ├── cleanup_static.py  # 静态文件清理工具
│   ├── download_models_hf.py # 模型下载脚本
│   ├── project_health_check.py # 项目健康检查
│   ├── start_service.py   # 增强启动脚本
│   └── test_service.py    # 服务测试脚本
├── static/                 # 静态文件服务
├── logs/                   # 应用日志
├── demo/                   # 测试样例文件
│   ├── img/              # 图片样例
│   ├── office/           # Office 文档样例
│   └── pdf/              # PDF 样例
├── docs/                   # 项目文档
├── cicd/                   # CI/CD 配置
├── vendor/                 # 第三方依赖
├── skynet-mesh            # Sidecar 服务二进制
└── resource/               # 模型文件与资源
    ├── dolphin_model/     # Dolphin 模型文件
    ├── mineru_model/      # Mineru 模型文件
    └── model_weight/      # 其他模型权重
```

## 🔧 API 接口

### Mineru API（PDF 处理）
- `POST /api/mineru/v1/parse_file` - 上传 PDF 文件进行处理
- `POST /api/mineru/v1/parse_url` - 通过 URL 处理 PDF

### Dolphin API（图片文档处理）
- `POST /api/dolphin/v1/parse_file` - 上传图片文档进行处理
- `POST /api/dolphin/v1/parse_url` - 通过 URL 处理图片文档

### Monkey API（OCR 处理）
- `POST /api/monkey/v1/parse_file` - 上传文档进行 OCR 处理
- `POST /api/monkey/v1/parse_url` - 通过 URL 进行 OCR 处理

### MarkItDown API（通用文档处理）
- `POST /api/markitdown/v1/parse_file` - 上传文档进行处理
- `POST /api/markitdown/v1/parse_url` - 通过 URL 处理文档

### 系统接口
- `GET /` - 服务信息
- `GET /health` - 健康检查
- `GET /docs` - API 文档（Swagger UI）
- `GET /redoc` - API 文档（ReDoc）

## 🧪 测试

### 运行测试
```bash
# 运行全部测试
python -m pytest test/

# 运行指定测试文件
python test/test_config.py
python test/test_integration.py
python test/test_api.py

# 项目健康检查
python script/project_health_check.py

# 服务健康测试
python script/test_service.py
```

### 手动测试
```bash
# 测试配置加载
python test/test_config.py

# 测试日志设置
python test/test_logger.py

# 测试优雅停机
python test/test_stop.py
```

## 📊 监控与日志

服务提供全面的日志与监控：

- **结构化日志**：JSON 格式日志，便于解析和分析
- **请求追踪**：支持 Trace ID 跟踪请求链路
- **健康监控**：内置健康检查接口和模型状态监控
- **性能指标**：处理时长、错误统计和资源使用情况
- **自动清理**：静态文件自动清理，防止磁盘空间耗尽

### 日志文件
- `logs/doc_convert.jsonl` - 主应用日志
- `logs/server.log` - 服务日志
- `logs/engine.log` - 处理引擎日志

## 🚀 部署

### Docker 部署
```bash
# 构建 Docker 镜像
bash script/build.sh
# 或者
docker build -f cicd/Dockerfile -t doc-convert:latest .

# 运行容器
docker run -p 11288:11288 -v $(pwd)/config:/app/config doc-convert:latest
```

### 生产环境部署
```bash
# 使用 Gunicorn 多进程部署
gunicorn main:app \
  --workers 4 \
  --worker-class uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:11288 \
  --access-logfile logs/access.log \
  --error-logfile logs/error.log

# 使用增强启动脚本
python script/start_service.py --host 0.0.0.0 --port 11288 --workers 4
```

## 🤝 贡献指南

1. Fork 本仓库
2. 创建功能分支（`git checkout -b feature/amazing-feature`）
3. 提交更改（`git commit -m 'Add amazing feature'`）
4. 推送分支（`git push origin feature/amazing-feature`）
5. 创建 Pull Request

## 📄 许可证

本项目基于 MIT 许可证，详情参见 [LICENSE](LICENSE) 文件。

## 🆘 支持

- **文档**：服务运行时访问 `/docs` 获取文档
- **问题反馈**：通过 GitHub Issues 报告 bug 或需求
- **健康检查**：通过 `/health` 接口监控服务状态

## 🔄 版本历史

- **v1.0.0**：首发版本，支持 Mineru、Dolphin、Monkey OCR 和 MarkItDown
- 支持多格式文档处理（PDF、图片、Office 文档）
- 完善的 RESTful API 和 OpenAPI 文档
- 生产级日志、监控和自动文件清理
- 支持动态模型加载和服务网格集成