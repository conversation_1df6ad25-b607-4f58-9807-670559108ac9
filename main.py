"""
Document Converter Service

A FastAPI-based service for converting various document formats (PDF, images, Office documents)
to Markdown using multiple AI models including Mine<PERSON>, Dolphin, and MarkItDown.

Author: Document Converter Team
License: MIT
"""

import logging
import os
from pathlib import Path

from server.app_factory import create_app
from utils.config import global_config

# 设置环境变量 - 使用相对路径
if "CONFIG_FILE_PATH" not in os.environ:
    project_root = Path(__file__).parent
    config_path = project_root / "config" / "config.ini"
    os.environ["CONFIG_FILE_PATH"] = str(config_path)

logger = logging.getLogger(__name__)

# 创建应用实例
app = create_app()

# 开发调试使用
if __name__ == "__main__":
    import uvicorn

    # 从配置文件读取端口，如果没有则使用默认值11288
    port = global_config.getint("server", "port", fallback=11288)
    host = global_config.get("server", "host", fallback="0.0.0.0")

    logger.info(f"Starting development server on {host}:{port}")
    uvicorn.run("main:app", host=host, port=port, log_level="info", workers=1)
