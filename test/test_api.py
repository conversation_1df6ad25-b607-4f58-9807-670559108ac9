"""
API tests for the document converter service.

This module contains tests for all API endpoints to ensure they work correctly.
"""

import os
import tempfile
from unittest.mock import Mock, patch

import pytest
from fastapi.testclient import TestClient

from main import create_app
from server.api.common import ParseHeader, ParseRequest, ParseReqPayload


@pytest.fixture
def client():
    """Create a test client for the FastAPI application."""
    app = create_app()
    return TestClient(app)


@pytest.fixture
def sample_pdf_file():
    """Create a sample PDF file for testing."""
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
        tmp.write(b'%PDF-1.4\n%Test PDF content')
        tmp_path = tmp.name
    yield tmp_path
    os.unlink(tmp_path)


@pytest.fixture
def sample_image_file():
    """Create a sample image file for testing."""
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
        # Simple PNG header
        tmp.write(b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde')
        tmp_path = tmp.name
    yield tmp_path
    os.unlink(tmp_path)


class TestSystemEndpoints:
    """Test system endpoints."""

    def test_root_endpoint(self, client):
        """Test the root endpoint returns service information."""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "service" in data
        assert "version" in data
        assert "docs" in data

    def test_health_endpoint(self, client):
        """Test the health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "document-converter"

    def test_docs_endpoint(self, client):
        """Test that API documentation is accessible."""
        response = client.get("/docs")
        assert response.status_code == 200

    def test_openapi_endpoint(self, client):
        """Test that OpenAPI schema is accessible."""
        response = client.get("/openapi.json")
        assert response.status_code == 200
        data = response.json()
        assert "openapi" in data
        assert "paths" in data


class TestFileValidation:
    """Test file validation functionality."""

    def test_file_size_validation(self, client):
        """Test file size validation."""
        # Create a file that's too large (simulate)
        large_content = b'x' * (101 * 1024 * 1024)  # 101MB
        
        with tempfile.NamedTemporaryFile(suffix='.pdf') as tmp:
            tmp.write(large_content)
            tmp.seek(0)
            
            response = client.post(
                "/api/mineru/parse_file",
                files={"file": ("large.pdf", tmp, "application/pdf")}
            )
            assert response.status_code == 413

    def test_invalid_file_extension(self, client):
        """Test invalid file extension handling."""
        with tempfile.NamedTemporaryFile(suffix='.txt') as tmp:
            tmp.write(b'test content')
            tmp.seek(0)
            
            response = client.post(
                "/api/mineru/parse_file",
                files={"file": ("test.txt", tmp, "text/plain")}
            )
            assert response.status_code == 400
            assert "不支持的文件格式" in response.json()["detail"]

    def test_empty_filename(self, client):
        """Test empty filename handling."""
        with tempfile.NamedTemporaryFile() as tmp:
            tmp.write(b'test content')
            tmp.seek(0)
            
            response = client.post(
                "/api/mineru/parse_file",
                files={"file": ("", tmp, "application/pdf")}
            )
            assert response.status_code == 400


class TestMineuAPI:
    """Test Mineru API endpoints."""

    @patch('server.api.mineru_api.doc_analyze')
    @patch('server.api.mineru_api.PymuDocDataset')
    @patch('server.api.mineru_api.FileBasedDataReader')
    def test_parse_file_success(self, mock_reader, mock_dataset, mock_analyze, client, sample_pdf_file):
        """Test successful PDF file parsing."""
        # Mock the parsing pipeline
        mock_reader_instance = Mock()
        mock_reader.return_value = mock_reader_instance
        mock_reader_instance.read.return_value = b'pdf content'
        
        mock_dataset_instance = Mock()
        mock_dataset.return_value = mock_dataset_instance
        mock_dataset_instance.classify.return_value = Mock()
        
        mock_infer_result = Mock()
        mock_dataset_instance.apply.return_value = mock_infer_result
        
        mock_pipe_result = Mock()
        mock_infer_result.pipe_ocr_mode.return_value = mock_pipe_result
        mock_pipe_result.get_markdown.return_value = "# Test Document\n\nContent"
        mock_pipe_result.dump_md.return_value = None
        
        with open(sample_pdf_file, 'rb') as f:
            response = client.post(
                "/api/mineru/parse_file",
                files={"file": ("test.pdf", f, "application/pdf")}
            )
        
        assert response.status_code == 200
        data = response.json()
        assert data["header"]["code"] == 0
        assert "markdown" in data["payload"]

    def test_parse_url_invalid_url(self, client):
        """Test URL parsing with invalid URL."""
        request_data = {
            "header": {"traceId": "test-123"},
            "payload": {"file_url": ""}
        }
        
        response = client.post("/api/mineru/parse_url", json=request_data)
        assert response.status_code == 400


class TestDolphinAPI:
    """Test Dolphin API endpoints."""

    @patch('server.api.dolphin_api.process_page')
    def test_parse_file_success(self, mock_process, client, sample_image_file):
        """Test successful image file parsing."""
        mock_process.return_value = "# Parsed Image Content\n\nExtracted text"
        
        with open(sample_image_file, 'rb') as f:
            response = client.post(
                "/api/dolphin/parse_file",
                files={"file": ("test.png", f, "image/png")}
            )
        
        assert response.status_code == 200
        data = response.json()
        assert data["header"]["code"] == 0
        assert "markdown" in data["payload"]


class TestMarkItDownAPI:
    """Test MarkItDown API endpoints."""

    @patch('server.api.markitdown_api.MarkItDown')
    def test_parse_file_success(self, mock_markitdown, client, sample_pdf_file):
        """Test successful document parsing with MarkItDown."""
        mock_md_instance = Mock()
        mock_markitdown.return_value = mock_md_instance
        
        mock_result = Mock()
        mock_result.text_content = "# Converted Document\n\nContent from MarkItDown"
        mock_md_instance.convert.return_value = mock_result
        
        with open(sample_pdf_file, 'rb') as f:
            response = client.post(
                "/api/markitdown/parse_file",
                files={"file": ("test.pdf", f, "application/pdf")}
            )
        
        assert response.status_code == 200
        data = response.json()
        assert data["header"]["code"] == 0
        assert "markdown" in data["payload"]


class TestErrorHandling:
    """Test error handling across all endpoints."""

    def test_missing_file(self, client):
        """Test handling of missing file in upload."""
        response = client.post("/api/mineru/parse_file")
        assert response.status_code == 422  # Validation error

    def test_invalid_json_payload(self, client):
        """Test handling of invalid JSON in URL endpoints."""
        response = client.post(
            "/api/mineru/parse_url",
            json={"invalid": "payload"}
        )
        assert response.status_code == 422  # Validation error

    @patch('server.api.common.httpx.AsyncClient')
    def test_url_download_failure(self, mock_client, client):
        """Test handling of URL download failures."""
        mock_client_instance = Mock()
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        mock_client_instance.get.side_effect = Exception("Network error")
        
        request_data = {
            "header": {"traceId": "test-123"},
            "payload": {"file_url": "https://example.com/test.pdf"}
        }
        
        response = client.post("/api/mineru/parse_url", json=request_data)
        assert response.status_code == 500


if __name__ == "__main__":
    pytest.main([__file__])
