#!/usr/bin/env python3
"""
Simple test to verify basic functionality.
"""

import sys
import os

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def test_basic_imports():
    """Test basic imports work."""
    print("Testing basic imports...")
    
    try:
        # Test configuration
        import utils.config
        print("✅ utils.config imported")
        
        # Test common utilities
        from server.api.common import validate_file_extension, validate_file_size
        print("✅ server.api.common imported")
        
        # Test validation functions
        assert validate_file_extension("test.pdf") == True
        assert validate_file_extension("test.txt") == False
        assert validate_file_size(1024) == True
        assert validate_file_size(200 * 1024 * 1024) == False
        print("✅ Validation functions work")
        
        # Test graceful stop
        from utils.graceful_stop.stop import register_signal
        print("✅ utils.graceful_stop imported")
        
        print("\n🎉 All basic tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_basic_imports()
    if success:
        print("\n✅ Basic functionality is working correctly.")
    else:
        print("\n❌ There are issues with the basic functionality.")
    
    sys.exit(0 if success else 1)
