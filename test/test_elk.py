import threading

import uvicorn
from fastapi import FastAPI, Request

from vendor.pandora import elk
from vendor.pandora.middleware.fastapi_middle import ContextMiddleware
from vendor.pandora.middleware.pandora_context import PandoraContext

app = FastAPI()

# 日志中间件，拦截日志打印，初始化request.state.pandora_context
app.add_middleware(ContextMiddleware)
elk.init_global_logger("ocr_server", log_path="./app.jsonl", level="DEBUG")


@app.post("/test")
async def test_endpoint(request: Request):
    pc: PandoraContext = request.state.pandora_context
    print(pc.context.get("traceId"))
    print(pc.context.get("headerTag"))

    s1 = pc.root_span.add_span("test1")
    s1.set_attribute("testnode", "test")
    s1.finish()
    
    # elk.get_global_logger().info(elk.proto.KafkaProto(SpanId="xxxx",Span=s1.to_json()))
    

    return {
        "traceId": pc.context.get("traceId"),
        "headerTag": pc.context.get("headerTag"),
    }


def client():
    import requests

    json_body = {
        "header": {
            "traceId": "test-trace-id-123",
            "skynet-tlb-service-tag-selector": "test-header-tag-456",
        },
        "test": "some_value",
    }

    response = requests.post("http://127.0.0.1:8000/test", json=json_body, timeout=100)

    print("状态码:", response.status_code)
    print("响应内容:", response.json())


if __name__ == "__main__":

    t = threading.Timer(3, client)
    t.start()
    uvicorn.run("test_elk:app", host="127.0.0.1", port=8000, reload=True)
