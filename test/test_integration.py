#!/usr/bin/env python3
"""
Integration tests for the document converter service.

This script tests the actual functionality using demo files.
"""

import os
import sys
import time
import requests
import subprocess
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_config_loading():
    """Test configuration loading."""
    print("Testing configuration loading...")
    try:
        import utils.config
        config = utils.config.global_config
        
        # Check if basic sections exist
        sections = config.sections()
        print(f"Configuration sections: {sections}")
        
        if 'version' in sections:
            print(f"Service title: {config.get('version', 'title')}")
            print(f"Service version: {config.get('version', 'version')}")
        
        if 'server' in sections:
            print(f"Server host: {config.get('server', 'host')}")
            print(f"Server port: {config.get('server', 'port')}")
        
        print("✅ Configuration loading test passed")
        return True
    except Exception as e:
        print(f"❌ Configuration loading test failed: {e}")
        return False

def test_imports():
    """Test that all modules can be imported."""
    print("\nTesting module imports...")
    modules_to_test = [
        'main',
        'server.api.common',
        'server.api.mineru_api',
        'server.api.dolphin_api', 
        'server.api.markitdown_api',
        'utils.config',
        'utils.graceful_stop.stop'
    ]
    
    failed_imports = []
    for module in modules_to_test:
        try:
            __import__(module)
            print(f"✅ {module}")
        except Exception as e:
            print(f"❌ {module}: {e}")
            failed_imports.append(module)
    
    if not failed_imports:
        print("✅ All module imports test passed")
        return True
    else:
        print(f"❌ Failed imports: {failed_imports}")
        return False

def test_file_validation():
    """Test file validation functions."""
    print("\nTesting file validation...")
    try:
        from server.api.common import validate_file_extension, validate_file_size
        
        # Test valid extensions
        assert validate_file_extension("test.pdf") == True
        assert validate_file_extension("test.png") == True
        assert validate_file_extension("test.docx") == True
        
        # Test invalid extensions
        assert validate_file_extension("test.txt") == False
        assert validate_file_extension("test.exe") == False
        assert validate_file_extension("") == False
        
        # Test file sizes
        assert validate_file_size(1024) == True  # 1KB
        assert validate_file_size(50 * 1024 * 1024) == True  # 50MB
        assert validate_file_size(200 * 1024 * 1024) == False  # 200MB
        assert validate_file_size(0) == False  # 0 bytes
        
        print("✅ File validation test passed")
        return True
    except Exception as e:
        print(f"❌ File validation test failed: {e}")
        return False

def test_demo_files_exist():
    """Test that demo files exist and are accessible."""
    print("\nTesting demo files...")
    demo_dir = project_root / "demo"
    
    required_dirs = ["img", "pdf", "office"]
    missing_dirs = []
    
    for dir_name in required_dirs:
        dir_path = demo_dir / dir_name
        if not dir_path.exists():
            missing_dirs.append(dir_name)
        else:
            files = list(dir_path.glob("*"))
            print(f"✅ {dir_name}: {len(files)} files found")
    
    if missing_dirs:
        print(f"❌ Missing demo directories: {missing_dirs}")
        return False
    else:
        print("✅ Demo files test passed")
        return True

def test_app_creation():
    """Test FastAPI app creation."""
    print("\nTesting FastAPI app creation...")
    try:
        from main import create_app
        app = create_app()
        
        # Check if app is created
        assert app is not None
        print("✅ App created successfully")
        
        # Check routes
        routes = [route.path for route in app.routes]
        expected_routes = ["/", "/health", "/docs", "/redoc"]
        
        for route in expected_routes:
            if route in routes:
                print(f"✅ Route {route} found")
            else:
                print(f"❌ Route {route} missing")
        
        print("✅ FastAPI app creation test passed")
        return True
    except Exception as e:
        print(f"❌ FastAPI app creation test failed: {e}")
        return False

def test_graceful_stop():
    """Test graceful stop functionality."""
    print("\nTesting graceful stop...")
    try:
        from utils.graceful_stop.stop import register_signal
        register_signal()
        print("✅ Graceful stop test passed")
        return True
    except Exception as e:
        print(f"❌ Graceful stop test failed: {e}")
        return False

def run_all_tests():
    """Run all tests and report results."""
    print("🚀 Starting Document Converter Integration Tests\n")
    print("=" * 60)
    
    tests = [
        test_config_loading,
        test_imports,
        test_file_validation,
        test_demo_files_exist,
        test_app_creation,
        test_graceful_stop
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print("-" * 40)
    
    print(f"\n📊 Test Results:")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 All tests passed! The optimization is working correctly.")
        return True
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
